// WebSocket连接管理
class AdminWebSocket {
    constructor() {
        this.ws = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000;
        this.isConnected = false;
        this.commandStatuses = new Map(); // 存储命令执行状态
    }

    connect() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        // 使用正确的WebSocket端口28765，而不是HTTP端口
        const url = new URL(window.location.origin);
        const wsUrl = `${protocol}//${url.hostname}:28765`;

        try {
            console.log(`尝试连接WebSocket: ${wsUrl}`);
            this.ws = new WebSocket(wsUrl);

            this.ws.onopen = () => {
                console.log('WebSocket连接已建立');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.updateConnectionStatus('connected');

                // 注册为管理控制台
                this.register();
            };

            this.ws.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                } catch (e) {
                    console.error('解析WebSocket消息失败:', e);
                }
            };

            this.ws.onclose = () => {
                console.log('WebSocket连接已关闭');
                this.isConnected = false;
                this.updateConnectionStatus('disconnected');
                this.scheduleReconnect();
            };

            this.ws.onerror = (error) => {
                console.error('WebSocket错误:', error);
                this.updateConnectionStatus('error');
            };

        } catch (e) {
            console.error('WebSocket连接失败:', e);
            this.scheduleReconnect();
        }
    }

    register() {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({
                type: 'register',
                id: `admin-console-${Date.now()}`,
                role: 'viewer',  // 改为viewer角色以接收视频流
                name: 'Web管理控制台'
            }));
        }
    }

    scheduleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            setTimeout(() => this.connect(), this.reconnectDelay);
        } else {
            console.error('WebSocket重连失败，已达到最大重试次数');
            this.updateConnectionStatus('failed');
        }
    }

    handleMessage(data) {
        switch (data.type) {
            case 'registered':
                console.log('管理控制台注册成功');
                // 请求初始设备列表
                admin.requestInitialDeviceList();
                break;

            case 'command_status':
                this.handleCommandStatus(data);
                break;

            case 'room_info_updated':
                this.handleRoomInfoUpdate(data);
                break;

            case 'device_list_response':
                // 处理设备列表响应
                admin.handleDeviceListResponse(data);
                break;

            case 'device_online':
                // 处理设备上线事件
                admin.handleDeviceOnline(data);
                break;

            case 'device_offline':
                // 处理设备下线事件
                admin.handleDeviceOffline(data);
                break;

            // device_info_response 不再需要，所有信息在初始列表中获取

            case 'boot_report':
                // 处理开机上报
                admin.handleBootReport(data);
                break;

            case 'command_response':
                // 处理命令响应
                this.handleCommandResponse(data);
                break;

            case 'screenshot_result':
                // 处理截屏结果
                this.handleScreenshotResult(data);
                break;

            case 'log_download_result':
                // 处理日志下载结果
                this.handleLogDownloadResult(data);
                break;

            case 'webrtc_signaling':
                // 处理WebRTC信令
                this.handleWebRTCSignaling(data);
                break;

            case 'remark_updated':
                // 处理备注更新响应
                this.handleRemarkUpdated(data);
                break;

            case 'offer':
                // 处理WebRTC offer
                this.handleWebRTCOffer(data);
                break;

            case 'answer':
                // 处理WebRTC answer
                this.handleWebRTCAnswer(data);
                break;

            case 'candidate':
                // 处理ICE候选
                this.handleWebRTCIceCandidate(data);
                break;

            case 'status_update':
                // 处理状态更新
                this.handleStatusUpdate(data);
                break;

            default:
                console.log('收到未知消息类型:', data.type);
        }
    }

    handleCommandStatus(data) {
        const { sender_id, command, success, message, progress } = data;

        // 更新命令状态存储
        this.commandStatuses.set(sender_id, {
            command,
            success,
            message,
            progress,
            timestamp: Date.now()
        });

        // 更新UI显示
        this.updateDeviceProgress(sender_id, command, success, message, progress);

        console.log(`设备 ${sender_id} 命令状态:`, { command, success, message, progress });
    }

    handleRoomInfoUpdate(data) {
        const { domain, room_count } = data;
        console.log(`服务器 ${domain} 房间信息已更新，共 ${room_count} 个房间`);

        // 请求更新的设备列表以显示最新的房间信息
        admin.requestInitialDeviceList();

        // 显示通知
        admin.showToast(`服务器 ${domain} 房间信息已更新 (${room_count} 个房间)`, 'info');
    }

    updateDeviceProgress(senderId, command, success, message, progress) {
        const deviceElement = document.querySelector(`[data-sender-id="${senderId}"]`);
        if (deviceElement) {
            let progressElement = deviceElement.querySelector('.device-progress');
            if (!progressElement) {
                progressElement = document.createElement('div');
                progressElement.className = 'device-progress';
                deviceElement.appendChild(progressElement);
            }

            const statusClass = success ? 'success' : 'error';
            const progressText = progress ? ` - ${progress}` : '';

            progressElement.innerHTML = `
                <div class="progress-item ${statusClass}">
                    <span class="command">${command}</span>
                    <span class="message">${message}${progressText}</span>
                    <span class="timestamp">${new Date().toLocaleTimeString()}</span>
                </div>
            `;

            // 3秒后淡出成功消息，错误消息保持显示
            if (success) {
                setTimeout(() => {
                    progressElement.style.opacity = '0.5';
                }, 3000);
            }
        }
    }

    // 处理命令响应
    handleCommandResponse(data) {
        const command = data.command;
        const success = data.success;
        const message = data.message || '';
        const deviceId = data.from || '未知设备';

        admin.log('命令响应', `${deviceId}: ${command} - ${message}`, success ? 'success' : 'error');

        admin.showToast(
            success ? 'success' : 'error',
            `命令响应 - ${deviceId}`,
            `${command}: ${message}`
        );
    }

    // 处理截屏结果
    handleScreenshotResult(data) {
        const requestId = data.request_id;
        const success = data.success;
        const fullUrl = data.full_url;
        const message = data.message || '';
        const deviceId = data.from || data.device_id || '未知设备';

        console.log('收到截屏结果:', { deviceId, requestId, success, fullUrl, originalData: data });
        admin.log('截屏结果', `${deviceId}: ${message}`, success ? 'success' : 'error');

        if (success && fullUrl) {
            console.log('截屏成功，准备显示预览:', { deviceId, fullUrl, requestId });
            admin.showToast('success', `截屏成功 - ${deviceId}`,
                `<a href="${fullUrl}" target="_blank">查看截屏</a>`);
            // 在设备列表中显示截屏结果
            admin.displayScreenshotResult(deviceId, fullUrl, requestId);
        } else {
            console.log('截屏失败:', { deviceId, success, fullUrl, message });
            admin.showToast('error', `截屏失败 - ${deviceId}`, message);
        }
    }

    // 处理状态更新
    handleStatusUpdate(data) {
        const statusType = data.status_type;
        const step = data.step;
        const progress = data.progress;
        const message = data.message || '';
        const deviceId = data.from || '未知设备';

        // 记录到操作日志
        admin.log('设备状态', `${deviceId}: [${statusType}] ${step} (${progress}%) - ${message}`, 'info');

        // 如果是升级相关的状态，显示提示
        if (statusType === 'upgrade') {
            admin.showToast('info', `升级进度 - ${deviceId}`, `${step}: ${message} (${progress}%)`);
        }
    }

    // 处理日志下载结果
    handleLogDownloadResult(data) {
        const success = data.success;
        const filename = data.filename;
        const message = data.message || '';
        const deviceId = data.from || '未知设备';

        admin.log('日志下载', `${deviceId}: ${message}`, success ? 'success' : 'error');

        if (success && filename) {
            const logUrl = `http://8.134.131.24:21275/${filename}`;
            admin.showToast('success', `日志下载成功 - ${deviceId}`,
                `<a href="${logUrl}" target="_blank">点击打开日志文件</a>`);

            // 自动打开日志文件
            window.open(logUrl, '_blank');

            // 在设备列表中显示日志下载结果
            admin.displayLogDownloadResult(deviceId, logUrl, filename);
        } else {
            admin.showToast('error', `日志下载失败 - ${deviceId}`, message);
        }
    }

    // 处理WebRTC信令
    handleWebRTCSignaling(data) {
        try {
            const { from, message } = data;
            console.log('收到WebRTC信令:', { from, message });

            // 查找对应的WebRTC连接
            if (admin.videoConnections && admin.videoConnections.has(from)) {
                const pc = admin.videoConnections.get(from);

                if (message.type === 'answer') {
                    // 处理answer
                    pc.setRemoteDescription(new RTCSessionDescription(message))
                        .then(() => {
                            console.log('设置远程描述成功');
                            const statusEl = document.querySelector(`#video-${from}`)?.parentNode?.querySelector('.video-status');
                            if (statusEl) {
                                statusEl.textContent = '正在连接...';
                                statusEl.style.background = 'rgba(255, 193, 7, 0.8)';
                            }
                        })
                        .catch(err => {
                            console.error('设置远程描述失败:', err);
                        });
                } else if (message.type === 'ice-candidate') {
                    // 处理ICE候选
                    pc.addIceCandidate(new RTCIceCandidate(message.candidate))
                        .then(() => {
                            console.log('添加ICE候选成功');
                        })
                        .catch(err => {
                            console.error('添加ICE候选失败:', err);
                        });
                }
            }
        } catch (error) {
            console.error('处理WebRTC信令失败:', error);
        }
    }

    // 处理WebRTC offer
    handleWebRTCOffer(data) {
        console.log('收到WebRTC offer:', data);
        // 管理界面作为接收端，不应该收到offer
        // 这里只是记录日志
    }

    // 处理WebRTC answer
    handleWebRTCAnswer(data) {
        try {
            const { from, sdp } = data;
            console.log('收到WebRTC answer:', { from, sdp });

            // 查找对应的WebRTC连接
            if (admin.videoConnections && admin.videoConnections.has(from)) {
                const pc = admin.videoConnections.get(from);

                // 处理answer
                const answer = new RTCSessionDescription({
                    type: 'answer',
                    sdp: sdp
                });

                pc.setRemoteDescription(answer)
                    .then(() => {
                        console.log('设置远程描述成功');
                        const statusEl = document.querySelector(`#video-${from}`)?.parentNode?.querySelector('.video-status');
                        if (statusEl) {
                            statusEl.textContent = '正在连接...';
                            statusEl.style.background = 'rgba(255, 193, 7, 0.8)';
                        }
                    })
                    .catch(err => {
                        console.error('设置远程描述失败:', err);
                    });
            }
        } catch (error) {
            console.error('处理WebRTC answer失败:', error);
        }
    }

    // 处理ICE候选
    handleWebRTCIceCandidate(data) {
        try {
            const { from, candidate } = data;
            console.log('收到ICE候选:', { from, candidate });

            // 查找对应的WebRTC连接
            if (admin.videoConnections && admin.videoConnections.has(from)) {
                const pc = admin.videoConnections.get(from);

                // 处理ICE候选
                pc.addIceCandidate(new RTCIceCandidate(candidate))
                    .then(() => {
                        console.log('添加ICE候选成功');
                    })
                    .catch(err => {
                        console.error('添加ICE候选失败:', err);
                    });
            }
        } catch (error) {
            console.error('处理ICE候选失败:', error);
        }
    }

    // 发送消息
    send(message) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
            return true;
        } else {
            console.warn('WebSocket未连接，无法发送消息:', message);
            return false;
        }
    }

    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connectionStatus');
        if (statusElement) {
            const statusMap = {
                'connected': { text: '已连接', class: 'connected' },
                'disconnected': { text: '已断开', class: 'disconnected' },
                'error': { text: '连接错误', class: 'error' },
                'failed': { text: '连接失败', class: 'failed' }
            };

            const statusInfo = statusMap[status] || { text: '未知状态', class: 'unknown' };
            statusElement.textContent = statusInfo.text;
            statusElement.className = `connection-status ${statusInfo.class}`;
        }
    }

    send(data) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
            return true;
        }
        return false;
    }
}

// 全局WebSocket实例
const adminWS = new AdminWebSocket();

// 常用标签点击自动填充到commandParams
function fillParamTag(label, value) {
    const paramBox = document.getElementById('commandParams');
    try {
        // 尝试合并到现有JSON
        let current = paramBox.value.trim();
        let currentObj = {};
        if (current) {
            currentObj = JSON.parse(current);
        }
        let tagObj = JSON.parse(value);
        // 合并
        Object.assign(currentObj, tagObj);
        paramBox.value = JSON.stringify(currentObj, null, 2);
    } catch (e) {
        // 直接覆盖
        paramBox.value = value;
    }
}

// controlCommand切换时自动填充默认参数
const commandDefaultParams = {
    'start_service': '{}',
    'stop_service': '{}',
    'restart_service': '{}',
    'change_resolution': '{"resolution":"1920x1080"}',
    'change_bitrate': '{"bitrate":3000}',
    'change_codec': '{"codec":"H264"}',
    'reboot_device': '{}',
    'set_auto_start_game': '{"auto_start":true}',
    'toggle_log_display': '{enabled: true}',
    'download_logs': '{}',
    'take_screenshot': '{}',
    'upgrade': '{"apk_url":"http://39.96.165.173:8888/down/XdFBq5q171Qu.apk","version":"2","force":false}'
};

document.addEventListener('DOMContentLoaded', function() {
    const cmdSelect = document.getElementById('controlCommand');
    if (cmdSelect) {
        cmdSelect.addEventListener('change', function() {
            const val = cmdSelect.value;
            if (commandDefaultParams[val]) {
                document.getElementById('commandParams').value = JSON.stringify(JSON.parse(commandDefaultParams[val]), null, 2);
            } else {
                document.getElementById('commandParams').value = '';
            }
        });
        // 页面加载时自动填充一次
        const initVal = cmdSelect.value;
        if (commandDefaultParams[initVal]) {
            document.getElementById('commandParams').value = JSON.stringify(JSON.parse(commandDefaultParams[initVal]), null, 2);
        }
    }
});
// 信令服务器管理控制台 JavaScript

class SignalingServerAdmin {
    constructor() {
        this.baseUrl = window.location.origin;
        this.apiBase = `${this.baseUrl}/api/v1`;
        this.senders = {};
        this.receivers = {};
        this.currentCommand = null; // 当前正在处理的命令
        this.currentDeviceId = null; // 当前操作的设备ID
        this.websocket = null; // WebSocket连接
        this.dropdownTimeout = null; // 下拉菜单防抖定时器
        this.groupedSenders = {}; // 按域名和分类分组的发送端
        this.init();
    }

    init() {
        this.log('系统', '管理控制台初始化中...', 'info');
        this.loadCurrentConfig();

        // 启动WebSocket连接
        adminWS.connect();

        // 签名由信令服务器处理，管理台不需要生成签名
        // 移除定期刷新，改为WebSocket事件驱动
        // 注意：不在这里请求设备列表，等WebSocket连接成功后再请求

        // 点击外部关闭下拉菜单
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.control-dropdown')) {
                const timestamp = new Date().toLocaleTimeString();
                const stack = new Error().stack;
                const clickedElement = event.target;
                const elementInfo = {
                    tagName: clickedElement.tagName,
                    className: clickedElement.className,
                    id: clickedElement.id,
                    textContent: clickedElement.textContent?.substring(0, 50) || ''
                };

                console.log(`🖱️ [${timestamp}] 点击外部关闭下拉菜单`);
                console.log('📍 点击的元素:', elementInfo);
                console.log('📍 调用堆栈:', stack);

                // 检查当前是否有显示的下拉菜单
                const visibleDropdowns = document.querySelectorAll('.dropdown-menu.show');
                if (visibleDropdowns.length > 0) {
                    console.log(`❌ [${timestamp}] 将关闭 ${visibleDropdowns.length} 个下拉菜单`);
                    visibleDropdowns.forEach(dropdown => {
                        const deviceId = dropdown.id.replace('dropdown-', '');
                        this.logDropdownStateChange(deviceId, 'show', 'hidden', '点击外部关闭', stack);
                    });
                }

                this.closeAllDropdowns();
            }
        });

        // WebSocket连接由AdminWebSocket类管理

        // 添加全局调试工具
        this.setupGlobalDebugTools();
    }

    // 设置全局调试工具
    setupGlobalDebugTools() {
        window.dropdownDebug = {
            // 查看状态日志
            viewLog: () => {
                if (window.dropdownStateLog && window.dropdownStateLog.length > 0) {
                    console.table(window.dropdownStateLog);
                    return window.dropdownStateLog;
                } else {
                    console.log('📝 暂无下拉菜单状态日志');
                    return [];
                }
            },

            // 清除日志
            clearLog: () => {
                window.dropdownStateLog = [];
                console.log('🗑️ 下拉菜单状态日志已清除');
            },

            // 查看当前显示的下拉菜单
            checkVisible: () => {
                const visible = document.querySelectorAll('.dropdown-menu.show');
                console.log(`📊 当前显示的下拉菜单数量: ${visible.length}`);
                visible.forEach((menu, index) => {
                    const deviceId = menu.id.replace('dropdown-', '');
                    const rect = menu.getBoundingClientRect();
                    console.log(`${index + 1}. ${deviceId}:`, {
                        位置: { left: rect.left, top: rect.top },
                        尺寸: { width: rect.width, height: rect.height },
                        是否在视口内: rect.left >= 0 && rect.top >= 0 && rect.right <= window.innerWidth && rect.bottom <= window.innerHeight
                    });
                });
                return visible;
            },

            // 强制显示指定菜单
            forceShow: (deviceId) => {
                const dropdown = document.getElementById(`dropdown-${deviceId}`);
                if (dropdown) {
                    dropdown.classList.add('show');
                    console.log(`✅ 强制显示菜单: ${deviceId}`);
                } else {
                    console.log(`❌ 未找到菜单: ${deviceId}`);
                }
            },

            // 强制隐藏所有菜单
            forceHideAll: () => {
                const visible = document.querySelectorAll('.dropdown-menu.show');
                visible.forEach(menu => {
                    menu.classList.remove('show');
                });
                console.log(`❌ 强制隐藏了 ${visible.length} 个菜单`);
            },

            // 监控菜单状态变化
            startMonitoring: () => {
                if (window.dropdownMonitor) {
                    console.log('⚠️ 监控已在运行');
                    return;
                }

                window.dropdownMonitor = setInterval(() => {
                    const visible = document.querySelectorAll('.dropdown-menu.show');
                    if (visible.length > 0) {
                        console.log(`👁️ 监控: ${visible.length} 个菜单显示中`);
                    }
                }, 2000);

                console.log('👁️ 开始监控下拉菜单状态 (每2秒检查一次)');
            },

            // 停止监控
            stopMonitoring: () => {
                if (window.dropdownMonitor) {
                    clearInterval(window.dropdownMonitor);
                    window.dropdownMonitor = null;
                    console.log('⏹️ 停止监控下拉菜单状态');
                } else {
                    console.log('⚠️ 监控未在运行');
                }
            },

            // 检测视觉闪烁
            detectFlicker: (deviceId) => {
                const dropdown = document.getElementById(`dropdown-${deviceId}`);
                if (!dropdown) {
                    console.log(`❌ 未找到菜单: ${deviceId}`);
                    return;
                }

                console.log(`🔍 开始检测菜单闪烁: ${deviceId}`);

                let frameCount = 0;
                let visibilityChanges = [];

                const checkVisibility = () => {
                    frameCount++;
                    const rect = dropdown.getBoundingClientRect();
                    const computedStyle = getComputedStyle(dropdown);
                    const isVisible = dropdown.classList.contains('show') &&
                                    computedStyle.display !== 'none' &&
                                    computedStyle.visibility !== 'hidden' &&
                                    computedStyle.opacity !== '0';

                    visibilityChanges.push({
                        frame: frameCount,
                        timestamp: performance.now(),
                        visible: isVisible,
                        rect: { left: rect.left, top: rect.top, width: rect.width, height: rect.height },
                        styles: {
                            display: computedStyle.display,
                            visibility: computedStyle.visibility,
                            opacity: computedStyle.opacity,
                            transform: computedStyle.transform
                        }
                    });

                    if (frameCount < 60) { // 检测1秒（60帧）
                        requestAnimationFrame(checkVisibility);
                    } else {
                        // 分析结果
                        const changes = [];
                        for (let i = 1; i < visibilityChanges.length; i++) {
                            if (visibilityChanges[i].visible !== visibilityChanges[i-1].visible) {
                                changes.push({
                                    frame: visibilityChanges[i].frame,
                                    from: visibilityChanges[i-1].visible,
                                    to: visibilityChanges[i].visible,
                                    timeDiff: visibilityChanges[i].timestamp - visibilityChanges[i-1].timestamp
                                });
                            }
                        }

                        console.log(`📊 闪烁检测结果 (${deviceId}):`, {
                            总帧数: frameCount,
                            可见性变化次数: changes.length,
                            变化详情: changes,
                            是否检测到闪烁: changes.length > 2
                        });

                        if (changes.length > 2) {
                            console.log(`⚠️ 检测到闪烁! 菜单在1秒内发生了 ${changes.length} 次可见性变化`);
                        } else {
                            console.log(`✅ 未检测到闪烁，菜单显示稳定`);
                        }
                    }
                };

                requestAnimationFrame(checkVisibility);
            }
        };

        console.log('🔧 下拉菜单调试工具已加载！');
        console.log('使用方法:');
        console.log('  dropdownDebug.viewLog() - 查看状态日志');
        console.log('  dropdownDebug.checkVisible() - 查看当前显示的菜单');
        console.log('  dropdownDebug.startMonitoring() - 开始监控');
        console.log('  dropdownDebug.stopMonitoring() - 停止监控');
        console.log('  dropdownDebug.detectFlicker("设备ID") - 检测视觉闪烁');

        // 初始化新的设备菜单系统
        this.initDeviceMenuSystem();
    }

    // ===== 新的设备菜单系统 =====

    // 初始化设备菜单系统
    initDeviceMenuSystem() {
        console.log('🎛️ 初始化新的设备菜单系统');

        // 创建菜单容器
        this.createMenuContainer();

        // 绑定全局点击事件
        document.addEventListener('click', (event) => {
            if (!event.target.closest('.device-menu-container') &&
                !event.target.closest('.device-menu-panel')) {
                this.hideAllDeviceMenus();
            }
        });

        // 绑定ESC键关闭
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                this.hideAllDeviceMenus();
            }
        });
    }

    // 创建菜单容器
    createMenuContainer() {
        // 移除旧的菜单容器
        const oldContainer = document.getElementById('device-menu-overlay');
        if (oldContainer) {
            oldContainer.remove();
        }

        // 创建新的菜单容器
        const overlay = document.createElement('div');
        overlay.id = 'device-menu-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 999998;
        `;

        document.body.appendChild(overlay);
        this.menuOverlay = overlay;
    }

    // 显示设备菜单
    showDeviceMenu(deviceId, triggerButton) {
        console.log('🎛️ 显示设备菜单:', deviceId);

        // 隐藏所有其他菜单
        this.hideAllDeviceMenus();

        // 创建菜单面板
        const menuPanel = this.createMenuPanel(deviceId);

        // 计算位置
        const position = this.calculateMenuPosition(triggerButton, menuPanel);

        // 设置位置
        menuPanel.style.left = `${position.left}px`;
        menuPanel.style.top = `${position.top}px`;

        // 添加到容器
        this.menuOverlay.appendChild(menuPanel);
        this.menuOverlay.style.pointerEvents = 'auto';

        // 激活按钮状态
        triggerButton.classList.add('menu-active');

        // 显示菜单（带动画）
        requestAnimationFrame(() => {
            menuPanel.classList.add('menu-visible');
        });

        // 记录当前活动菜单
        this.activeMenuId = deviceId;
        this.activeTrigger = triggerButton;

        console.log('✅ 设备菜单显示完成:', deviceId);
    }

    // 创建菜单面板
    createMenuPanel(deviceId) {
        const panel = document.createElement('div');
        panel.className = 'device-menu-panel';
        panel.id = `device-menu-${deviceId}`;

        panel.innerHTML = `
            <div class="device-menu-grid">
                <div class="device-menu-section">
                    <h4>服务控制</h4>
                    <button class="device-menu-item item-success" onclick="admin.executeMenuAction('${deviceId}', 'start_service')">
                        🚀 启动服务
                    </button>
                    <button class="device-menu-item item-danger" onclick="admin.executeMenuAction('${deviceId}', 'stop_service')">
                        ⏹️ 停止服务
                    </button>
                    <button class="device-menu-item item-warning" onclick="admin.executeMenuAction('${deviceId}', 'restart_service')">
                        🔄 重启服务
                    </button>
                    <h4>视频控制</h4>
                    <button class="device-menu-item item-info" onclick="admin.showVideoControls('${deviceId}')">
                        🎥 视频参数设置
                    </button>
                    <button class="device-menu-item item-primary" onclick="admin.executeMenuAction('${deviceId}', 'take_screenshot')">
                        📸 视频流截屏
                    </button>
                </div>
                <div class="device-menu-section">
                    <h4>游戏控制</h4>
                    <button class="device-menu-item item-info" onclick="admin.showGameControls('${deviceId}')">
                        🎮 游戏设置
                    </button>
                    <h4>日志管理</h4>
                    <button class="device-menu-item item-success" onclick="admin.executeMenuAction('${deviceId}', 'toggle_log_display', {enabled: true})">
                        📝 开启日志显示
                    </button>
                    <button class="device-menu-item item-warning" onclick="admin.executeMenuAction('${deviceId}', 'toggle_log_display', {enabled: false})">
                        🚫 关闭日志显示
                    </button>
                    <button class="device-menu-item item-primary" onclick="admin.executeMenuAction('${deviceId}', 'download_logs')">
                        📥 下载日志(FTP)
                    </button>
                </div>
                <div class="device-menu-section">
                    <h4>网络配置</h4>
                    <button class="device-menu-item item-info" onclick="admin.showStunTurnModal('${deviceId}')">
                        🌐 STUN/TURN配置
                    </button>
                    <button class="device-menu-item item-success" onclick="admin.sendConfigToDevice('${deviceId}')">
                        📡 发送网络配置
                    </button>
                    <h4>系统控制</h4>
                    <button class="device-menu-item item-danger" onclick="admin.executeMenuAction('${deviceId}', 'reboot_device')">
                        🔄 重启设备
                    </button>
                    <button class="device-menu-item item-warning" onclick="admin.showUpgradeDialog('${deviceId}')">
                        📦 升级应用
                    </button>
                </div>
            </div>
        `;

        return panel;
    }

    // 计算菜单位置
    calculateMenuPosition(triggerButton, menuPanel) {
        const buttonRect = triggerButton.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        // 临时添加到DOM以获取尺寸
        menuPanel.style.visibility = 'hidden';
        menuPanel.style.display = 'block';
        this.menuOverlay.appendChild(menuPanel);

        const menuRect = menuPanel.getBoundingClientRect();
        const menuWidth = menuRect.width;
        const menuHeight = menuRect.height;

        // 移除临时添加的元素
        this.menuOverlay.removeChild(menuPanel);
        menuPanel.style.visibility = '';
        menuPanel.style.display = '';

        // 默认位置：按钮下方左对齐
        let left = buttonRect.left;
        let top = buttonRect.bottom + 10;

        // 右边界检查
        if (left + menuWidth > windowWidth - 20) {
            left = buttonRect.right - menuWidth;
        }

        // 左边界检查
        if (left < 20) {
            left = 20;
        }

        // 下边界检查
        if (top + menuHeight > windowHeight - 20) {
            top = buttonRect.top - menuHeight - 10;
        }

        // 上边界检查
        if (top < 20) {
            top = 20;
        }

        console.log('📍 菜单位置计算:', { left, top, menuWidth, menuHeight });

        return { left, top };
    }

    // 隐藏所有设备菜单
    hideAllDeviceMenus() {
        if (this.menuOverlay) {
            // 移除所有菜单面板
            this.menuOverlay.innerHTML = '';
            this.menuOverlay.style.pointerEvents = 'none';
        }

        // 移除所有按钮的激活状态
        document.querySelectorAll('.device-menu-trigger.menu-active').forEach(button => {
            button.classList.remove('menu-active');
        });

        // 清除活动菜单记录
        this.activeMenuId = null;
        this.activeTrigger = null;

        console.log('❌ 所有设备菜单已隐藏');
    }

    // 执行菜单动作
    async executeMenuAction(deviceId, action, params = {}) {
        console.log('⚡ 执行菜单动作:', { deviceId, action, params });

        // 隐藏菜单
        this.hideAllDeviceMenus();

        try {
            // 调用原有的快速命令方法
            await this.sendQuickCommand(deviceId, action, params);
        } catch (error) {
            console.error('❌ 菜单动作执行失败:', error);
            this.log('菜单动作', `${action} -> ${deviceId}: ${error.message}`, 'error');
        }
    }

    // 日志记录
    log(category, message, type = 'info') {
        const logArea = document.getElementById('logArea');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        logEntry.innerHTML = `[${timestamp}] [${category}] ${message}`;
        
        logArea.appendChild(logEntry);
        logArea.scrollTop = logArea.scrollHeight;
        
        // 限制日志条数
        const entries = logArea.children;
        if (entries.length > 100) {
            logArea.removeChild(entries[0]);
        }
    }

    // 签名功能已移除，由信令服务器负责签名

    // API请求封装
    async apiRequest(endpoint, method = 'GET', data = null) {
        try {
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(`${this.apiBase}${endpoint}`, options);
            const result = await response.json();

            if (!response.ok) {
                throw new Error(result.error || `HTTP ${response.status}`);
            }

            return result;
        } catch (error) {
            this.log('API错误', `${method} ${endpoint}: ${error.message}`, 'error');
            throw error;
        }
    }

    // 请求初始设备列表
    async requestInitialDeviceList() {
        try {
            this.log('系统', '请求初始设备列表...', 'info');

            // 通过WebSocket请求设备列表
            if (adminWS.ws && adminWS.ws.readyState === WebSocket.OPEN) {
                const success = adminWS.send({
                    type: 'request_device_list',
                    timestamp: Date.now()
                });

                if (!success) {
                    this.log('系统', 'WebSocket发送失败，1秒后重试', 'warning');
                    setTimeout(() => this.requestInitialDeviceList(), 1000);
                }
            } else {
                // 如果WebSocket未连接，等待连接后再请求
                this.log('系统', 'WebSocket未连接，1秒后重试', 'warning');
                setTimeout(() => this.requestInitialDeviceList(), 1000);
            }
        } catch (error) {
            this.log('系统', `请求设备列表失败: ${error.message}`, 'error');
        }
    }

    // 处理WebSocket设备列表响应（初始加载时的完整更新）
    handleDeviceListResponse(data) {
        try {
            if (data.senders) {
                this.senders = data.senders;
                this.groupSenders(); // 按域名和分类分组
                this.updateSendersDisplay(); // 初始加载时完整更新
                this.updateSendersCheckbox();
                this.log('系统', `收到发送端列表: ${Object.keys(data.senders).length} 个设备`, 'success');
            }

            if (data.receivers) {
                this.receivers = data.receivers;
                this.updateReceiversDisplay(); // 初始加载时完整更新
                this.updateReceiversCheckbox();
                this.log('系统', `收到接收端列表: ${Object.keys(data.receivers).length} 个设备`, 'success');
            }
        } catch (error) {
            this.log('系统', `处理设备列表响应失败: ${error.message}`, 'error');
        }
    }

    // 处理设备上线事件
    handleDeviceOnline(data) {
        try {
            const { device_id, device_type, device_info } = data;

            // 直接更新设备状态，不再检查缓存
            this.updateDeviceStatus(device_id, device_type, true, device_info);

            this.log('设备', `设备上线: ${device_id} (${device_type})`, 'success');
        } catch (error) {
            this.log('设备', `处理设备上线事件失败: ${error.message}`, 'error');
        }
    }

    // 处理设备下线事件
    handleDeviceOffline(data) {
        try {
            const { device_id, device_type } = data;
            this.updateDeviceStatus(device_id, device_type, false);
            this.log('设备', `设备下线: ${device_id} (${device_type})`, 'warning');
        } catch (error) {
            this.log('设备', `处理设备下线事件失败: ${error.message}`, 'error');
        }
    }

    // 不再需要复杂的设备信息检查和请求逻辑

    // 更新设备状态
    updateDeviceStatus(deviceId, deviceType, online, deviceInfo = null) {
        try {
            if (deviceType === 'sender') {
                if (!this.senders[deviceId]) {
                    this.senders[deviceId] = {};
                }

                this.senders[deviceId].online = online;
                this.senders[deviceId].last_seen = Date.now();

                if (deviceInfo) {
                    Object.assign(this.senders[deviceId], deviceInfo);
                }

                this.groupSenders(); // 重新分组
                this.updateSingleSender(deviceId); // 只更新单个设备
                this.updateSendersCheckbox();
            } else if (deviceType === 'receiver') {
                if (!this.receivers[deviceId]) {
                    this.receivers[deviceId] = {};
                }

                this.receivers[deviceId].online = online;
                this.receivers[deviceId].last_seen = Date.now();

                if (deviceInfo) {
                    Object.assign(this.receivers[deviceId], deviceInfo);
                }

                this.updateSingleReceiver(deviceId); // 只更新单个设备
                this.updateReceiversCheckbox();
            }
        } catch (error) {
            this.log('设备', `更新设备状态失败: ${deviceId} - ${error.message}`, 'error');
        }
    }

    // 不再需要处理完整设备信息响应，所有信息在初始列表中获取

    // 处理开机上报
    handleBootReport(data) {
        try {
            const { device_id, device_info } = data;

            if (device_info) {
                // 更新设备信息
                if (!this.senders[device_id]) {
                    this.senders[device_id] = {};
                }

                Object.assign(this.senders[device_id], device_info);
                this.senders[device_id].online = true;
                this.senders[device_id].last_boot = Date.now();

                // 不再需要缓存

                // 更新显示（增量更新）
                this.groupSenders();
                this.updateSingleSender(device_id);
                this.updateSendersCheckbox();

                this.log('设备', `收到开机上报: ${device_id}`, 'success');
            }
        } catch (error) {
            this.log('设备', `处理开机上报失败: ${error.message}`, 'error');
        }
    }

    // 加载当前配置
    async loadCurrentConfig() {
        try {
            const result = await this.apiRequest('/config');
            const config = result.config;

            // 填充STUN服务器
            document.getElementById('stunServers').value = config.stun_servers.join('\n');

            // 填充TURN服务器
            document.getElementById('turnServers').value = JSON.stringify(config.turn_servers, null, 2);

            this.log('配置', '当前配置已加载', 'success');
        } catch (error) {
            this.log('配置', `加载配置失败: ${error.message}`, 'error');
        }
    }

    // 更新配置
    async updateConfig(autoBroadcast = false) {
        try {
            const stunServers = document.getElementById('stunServers').value
                .split('\n')
                .map(s => s.trim())
                .filter(s => s);

            const turnServersText = document.getElementById('turnServers').value.trim();
            let turnServers = [];
            
            if (turnServersText) {
                turnServers = JSON.parse(turnServersText);
            }

            const configData = {
                config: {
                    stun_servers: stunServers,
                    turn_servers: turnServers
                }
            };

            if (autoBroadcast) {
                configData.auto_broadcast = true;
            }

            const result = await this.apiRequest('/config', 'POST', configData);
            this.log('配置', result.message, 'success');
            
            if (result.broadcast) {
                this.log('配置', result.broadcast, 'info');
            }
        } catch (error) {
            this.log('配置', `更新配置失败: ${error.message}`, 'error');
        }
    }

    // 广播配置
    async broadcastConfig() {
        try {
            const result = await this.apiRequest('/config/broadcast', 'POST');
            this.log('配置', result.message, 'success');
        } catch (error) {
            this.log('配置', `广播配置失败: ${error.message}`, 'error');
        }
    }

    // 刷新发送端列表（改为WebSocket方式）
    async refreshSenders() {
        try {
            this.log('发送端', '通过WebSocket请求发送端列表...', 'info');
            this.requestInitialDeviceList();
        } catch (error) {
            this.log('发送端', `刷新发送端列表失败: ${error.message}`, 'error');
        }
    }

    // 按域名和分类分组发送端
    groupSenders() {
        this.groupedSenders = {};

        // 处理发送端数据，兼容不同的数据结构
        const sendersArray = Array.isArray(this.senders) ? this.senders : Object.values(this.senders);

        sendersArray.forEach(sender => {
            // 兼容不同的数据结构
            const domain = sender.room_server_domain || '未分配服务器';
            const categoryId = sender.room_category_id || 0;
            const categoryName = this.getCategoryName(categoryId);

            if (!this.groupedSenders[domain]) {
                this.groupedSenders[domain] = {};
            }

            if (!this.groupedSenders[domain][categoryName]) {
                this.groupedSenders[domain][categoryName] = [];
            }

            // 确保发送端对象有必要的字段
            const processedSender = {
                ...sender,
                sender_id: sender.sender_id || sender.id || 'unknown',
                online: sender.online || sender.is_online || false,
                ip: sender.ip || sender.local_ip || sender.client_ip || '未知',
                room_name: sender.room_name || '',
                room_server_domain: sender.room_server_domain || '',
                room_category_id: sender.room_category_id || 0,
                room_sort_order: sender.room_sort_order || 0
            };

            this.groupedSenders[domain][categoryName].push(processedSender);
        });

        // 对每个分类内的设备按排序字段排序
        Object.keys(this.groupedSenders).forEach(domain => {
            Object.keys(this.groupedSenders[domain]).forEach(category => {
                this.groupedSenders[domain][category].sort((a, b) => {
                    // 先按排序字段，再按房间名称
                    const sortA = a.room_sort_order || 0;
                    const sortB = b.room_sort_order || 0;
                    if (sortA !== sortB) {
                        return sortA - sortB;
                    }
                    return (a.room_name || '').localeCompare(b.room_name || '');
                });
            });
        });
    }

    // 获取分类名称
    getCategoryName(categoryId) {
        const categoryMap = {
            0: '未分类',
            1: '街机游戏',
            2: '休闲游戏',
            3: '棋牌游戏',
            4: '体感游戏',
            5: 'VR游戏',
            6: '娃娃机',
            7: '测试设备'
        };
        return categoryMap[categoryId] || `分类${categoryId}`;
    }

    // 刷新接收端列表（改为WebSocket方式）
    async refreshReceivers() {
        try {
            this.log('接收端', '通过WebSocket请求接收端列表...', 'info');
            this.requestInitialDeviceList();
        } catch (error) {
            this.log('接收端', `刷新接收端列表失败: ${error.message}`, 'error');
        }
    }

    // 更新发送端状态显示（按域名和分类分组）
    updateSendersDisplay() {
        const container = document.getElementById('sendersStatus');
        if (!container) return;

        // 保存当前活跃的视频连接状态
        const activeVideoConnections = new Map();
        if (this.videoConnections) {
            for (const [deviceId, pc] of this.videoConnections.entries()) {
                if (pc.connectionState === 'connected' || pc.connectionState === 'connecting') {
                    activeVideoConnections.set(deviceId, {
                        connectionState: pc.connectionState,
                        iceConnectionState: pc.iceConnectionState
                    });
                }
            }
        }

        container.innerHTML = '';

        if (Object.keys(this.senders).length === 0) {
            container.innerHTML = '<p style="text-align: center; color: #6c757d;">暂无发送端数据</p>';
            return;
        }

        // 按域名和分类分组显示
        Object.entries(this.groupedSenders).forEach(([domain, categories]) => {
            const domainGroup = document.createElement('div');
            domainGroup.className = 'domain-group';
            domainGroup.setAttribute('data-domain', domain); // 添加域名属性以支持增量更新

            const domainHeader = document.createElement('div');
            domainHeader.className = 'domain-group-header';

            const totalDevices = Object.values(categories).reduce((sum, devices) => sum + devices.length, 0);

            // 统计该域名下的在线/离线设备
            let domainOnlineCount = 0;
            let domainOfflineCount = 0;
            Object.values(categories).forEach(devices => {
                devices.forEach(device => {
                    if (device.online) {
                        domainOnlineCount++;
                    } else {
                        domainOfflineCount++;
                    }
                });
            });

            domainHeader.innerHTML = `
                <div class="domain-title-section">
                    <h3 class="domain-group-title">🌐 ${domain}</h3>
                </div>
                <div class="domain-stats">
                    <div class="domain-stat-item online">
                        <span class="domain-stat-label">在线</span>
                        <span class="domain-stat-value">${domainOnlineCount}</span>
                    </div>
                    <div class="domain-stat-item offline">
                        <span class="domain-stat-label">离线</span>
                        <span class="domain-stat-value">${domainOfflineCount}</span>
                    </div>
                    <div class="domain-stat-item total">
                        <span class="domain-stat-label">总计</span>
                        <span class="domain-stat-value">${totalDevices}</span>
                    </div>
                </div>
                <div class="domain-batch-controls">
                    <button class="batch-btn" onclick="admin.batchVideoPreview('${domain}')" title="批量视频预览">
                        📺 批量预览
                    </button>
                    <button class="batch-btn" onclick="admin.batchVideoSettings('${domain}')" title="批量视频参数设置">
                        🎥 批量视频设置
                    </button>
                    <button class="batch-btn" onclick="admin.batchGameSettings('${domain}')" title="批量游戏设置">
                        🎮 批量游戏设置
                    </button>
                    <button class="batch-btn" onclick="admin.batchScreenshot('${domain}')" title="批量截屏">
                        📸 批量截屏
                    </button>
                    <button class="batch-btn" onclick="admin.batchDownloadLogs('${domain}')" title="批量下载日志">
                        📋 批量日志
                    </button>
                    <button class="batch-btn" onclick="admin.batchGameTest('${domain}')" title="批量游戏测试">
                        🎯 批量测试
                    </button>
                </div>
            `;

            const categoriesContainer = document.createElement('div');
            categoriesContainer.className = 'domain-categories';

            Object.entries(categories).forEach(([categoryName, devices]) => {
                const categoryGroup = document.createElement('div');
                categoryGroup.className = 'category-group';
                categoryGroup.setAttribute('data-category', categoryName); // 添加分类属性以支持增量更新

                const categoryHeader = document.createElement('div');
                categoryHeader.className = 'category-group-header';

                // 统计该分类下的在线/离线设备
                let categoryOnlineCount = 0;
                let categoryOfflineCount = 0;
                devices.forEach(device => {
                    if (device.online) {
                        categoryOnlineCount++;
                    } else {
                        categoryOfflineCount++;
                    }
                });

                categoryHeader.innerHTML = `
                    <div class="category-title-section">
                        <h4 class="category-group-title">🎮 ${categoryName}</h4>
                    </div>
                    <div class="category-stats">
                        <div class="category-stat-item online">
                            <span class="category-stat-label">在线</span>
                            <span class="category-stat-value">${categoryOnlineCount}</span>
                        </div>
                        <div class="category-stat-item offline">
                            <span class="category-stat-label">离线</span>
                            <span class="category-stat-value">${categoryOfflineCount}</span>
                        </div>
                        <div class="category-stat-item total">
                            <span class="category-stat-label">总计</span>
                            <span class="category-stat-value">${devices.length}</span>
                        </div>
                    </div>
                `;

                const devicesContainer = document.createElement('div');
                devicesContainer.className = 'category-devices';

                devices.forEach(sender => {
                    const card = this.createSenderCard(sender);
                    devicesContainer.appendChild(card);
                });

                categoryGroup.appendChild(categoryHeader);
                categoryGroup.appendChild(devicesContainer);
                categoriesContainer.appendChild(categoryGroup);
            });

            domainGroup.appendChild(domainHeader);
            domainGroup.appendChild(categoriesContainer);
            container.appendChild(domainGroup);
        });

        // 更新统计数据
        this.updateStats();

        // 恢复活跃的视频连接(暂不自动恢复)
        //this.restoreVideoConnections(activeVideoConnections);
    }

    // 恢复活跃的视频连接
    restoreVideoConnections(activeVideoConnections) {
        if (!activeVideoConnections || activeVideoConnections.size === 0) {
            return;
        }

        console.log('恢复视频连接:', activeVideoConnections);

        for (const [deviceId, connectionInfo] of activeVideoConnections.entries()) {
            try {
                // 检查设备是否仍然在线
                const device = this.senders[deviceId];
                if (!device || !device.online) {
                    console.log(`设备 ${deviceId} 已离线，跳过视频连接恢复`);
                    continue;
                }

                // 检查是否已有视频预览界面
                const videoElement = document.getElementById(`video-${deviceId}`);
                if (!videoElement) {
                    console.log(`设备 ${deviceId} 的视频预览界面已被移除，重新创建`);
                    // 重新显示视频预览
                    setTimeout(() => {
                        this.showVideoPreview(deviceId);
                    }, 1000);
                } else {
                    console.log(`设备 ${deviceId} 的视频预览界面仍存在，保持连接`);

                    // 检查WebRTC连接状态
                    if (this.videoConnections && this.videoConnections.has(deviceId)) {
                        const pc = this.videoConnections.get(deviceId);
                        const statusEl = videoElement.parentNode?.querySelector('.video-status');

                        if (statusEl) {
                            statusEl.textContent = `连接状态: ${pc.connectionState}`;

                            if (pc.connectionState === 'connected') {
                                statusEl.style.background = 'rgba(40, 167, 69, 0.8)';
                            } else if (pc.connectionState === 'connecting') {
                                statusEl.style.background = 'rgba(255, 193, 7, 0.8)';
                            } else {
                                statusEl.style.background = 'rgba(220, 53, 69, 0.8)';
                            }
                        }
                    }
                }
            } catch (error) {
                console.error(`恢复设备 ${deviceId} 视频连接失败:`, error);
            }
        }
    }

    // 更新统计数据
    updateStats() {
        const onlineCountEl = document.getElementById('onlineCount');
        const offlineCountEl = document.getElementById('offlineCount');
        const totalCountEl = document.getElementById('totalCount');

        if (!onlineCountEl || !offlineCountEl || !totalCountEl) return;

        let onlineCount = 0;
        let offlineCount = 0;
        let totalCount = 0;

        // 统计发送端状态
        Object.values(this.senders).forEach(sender => {
            totalCount++;
            if (sender.online) {
                onlineCount++;
            } else {
                offlineCount++;
            }
        });

        // 更新显示
        onlineCountEl.textContent = onlineCount;
        offlineCountEl.textContent = offlineCount;
        totalCountEl.textContent = totalCount;

        // 添加动画效果
        [onlineCountEl, offlineCountEl, totalCountEl].forEach(el => {
            el.style.transform = 'scale(1.1)';
            setTimeout(() => {
                el.style.transform = 'scale(1)';
            }, 200);
        });
    }

    // 创建发送端卡片
    createSenderCard(sender) {
        const card = document.createElement('div');
        card.className = 'sender-card';

        // 获取发送端ID，兼容不同的数据结构
        const senderId = sender.sender_id || sender.id || 'unknown';
        card.setAttribute('data-sender-id', senderId);
        card.setAttribute('data-device-id', senderId); // 添加设备ID属性以支持增量更新

        const onlineStatus = sender.online ?
            '<span class="status-online">● 在线</span>' :
            '<span class="status-offline">● 离线</span>';

        // 房间信息显示
        const roomInfo = sender.room_name ?
            `<div class="room-info">🏠 ${sender.room_name}</div>` :
            '<div class="room-info">🏠 未分配房间</div>';

        // 设备基本信息
        const deviceInfo = `
            <div class="device-basic-info">
                <div class="device-id">ID: ${senderId}</div>
                <div class="device-ip">IP: ${sender.ip || sender.local_ip || sender.client_ip || '未知'}</div>
            </div>
        `;

        // 计算最近上线时间
        const lastOnlineTime = this.getLastOnlineTime(sender);

        card.innerHTML = `
            <!-- 媒体显示区域放在最顶部 -->
            <div class="media-area" style="display: none;"></div>

            <h3>🎮 ${senderId}</h3>
            ${roomInfo}
            ${deviceInfo}
            <div class="device-info">
                <div class="device-info-item">
                    <span class="device-info-label">状态:</span>
                    <span class="device-info-value">${onlineStatus}</span>
                </div>
                <div class="device-info-item">
                    <span class="device-info-label">分辨率:</span>
                    <span class="device-info-value">${sender.resolution || sender.screen_resolution || '未知'}</span>
                </div>
                <div class="device-info-item">
                    <span class="device-info-label">观看者:</span>
                    <span class="device-info-value">${sender.viewers || 0}</span>
                </div>
                <div class="device-info-item">
                    <span class="device-info-label">最近上线:</span>
                    <span class="device-info-value last-online ${sender.online ? '' : 'offline'}">${lastOnlineTime}</span>
                </div>
                <div class="device-info-item">
                    <span class="device-info-label">备注:</span>
                    <input type="text" class="device-remark-input"
                           value="${sender.remark || ''}"
                           placeholder="点击添加备注..."
                           onchange="admin.updateDeviceRemark('${senderId}', this.value)"
                           onclick="event.stopPropagation()">
                </div>
            </div>
            <div class="device-controls">
                <div class="device-menu-container">
                    <button class="device-menu-trigger" onclick="admin.showDeviceMenu('${senderId}', this)">
                        ⚙️
                    </button>
                    <div class="device-menu">
                        <button id="video-btn-${senderId}" onclick="event.stopPropagation(); admin.toggleVideoPreview('${senderId}'); admin.hideDeviceMenu('${senderId}');" ${!sender.online ? 'disabled' : ''}>
                            📺 视频预览
                        </button>
                        <button onclick="admin.sendQuickCommand('${senderId}', '${sender.online ? 'restart_service' : 'start_service'}')">
                            ${sender.online ? '🔄 重启服务' : '▶️ 启动服务'}
                        </button>
                        <button onclick="admin.sendQuickCommand('${senderId}', 'take_screenshot')">📸 截图</button>
                        <button onclick="admin.sendQuickCommand('${senderId}', 'download_logs')">📋 下载日志</button>
                        <button onclick="admin.startGameTest('${senderId}')">🎯 游戏测试</button>
                        <button onclick="admin.sendQuickCommand('${senderId}', 'reboot_device')">🔄 重启设备</button>
                        <button onclick="admin.showCommandModal('${senderId}')">⚙️ 高级命令</button>
                    </div>
                </div>
            </div>
        `;

        return card;
    }

    // 获取最近上线时间
    getLastOnlineTime(status) {
        if (status.online) {
            // 如果在线，显示连接时间
            if (status.connection_time) {
                const connectTime = new Date(status.connection_time * 1000);
                return `在线 (${connectTime.toLocaleString()})`;
            }
            return '当前在线';
        }

        if (status.last_heartbeat) {
            const lastTime = new Date(status.last_heartbeat * 1000);
            const now = new Date();
            const diffMs = now - lastTime;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);
            const diffDays = Math.floor(diffHours / 24);

            // 显示确切时间和相对时间
            const exactTime = lastTime.toLocaleString();
            let relativeTime = '';

            if (diffMins < 1) relativeTime = '刚刚';
            else if (diffMins < 60) relativeTime = `${diffMins}分钟前`;
            else if (diffHours < 24) relativeTime = `${diffHours}小时前`;
            else relativeTime = `${diffDays}天前`;

            return `${exactTime} (${relativeTime})`;
        }

        return '未知';
    }

    // 更新接收端状态显示
    updateReceiversDisplay() {
        const container = document.getElementById('receiversStatus');
        if (!container) return;

        container.innerHTML = '';

        if (Object.keys(this.receivers).length === 0) {
            container.innerHTML = '<p style="text-align: center; color: #6c757d;">暂无接收端数据</p>';
            return;
        }

        Object.entries(this.receivers).forEach(([id, status]) => {
            const card = document.createElement('div');
            card.className = 'receiver-card';
            card.setAttribute('data-device-id', id); // 添加设备ID属性以支持增量更新

            const onlineStatus = status.online ?
                '<span class="status-online">● 在线</span>' :
                '<span class="status-offline">● 离线</span>';

            card.innerHTML = `
                <h3>${id}</h3>
                <p><strong>状态:</strong> ${onlineStatus}</p>
                <p><strong>推流:</strong> ${status.streaming_status || '未知'}</p>
                <p><strong>服务:</strong> ${status.service_status || '未知'}</p>
                <p><strong>分辨率:</strong> ${status.resolution || '未知'}</p>
                <p><strong>码率:</strong> ${status.bitrate || '未知'} kbps</p>
                <p><strong>编码:</strong> ${status.codec || '未知'}</p>
                <p><strong>连接数:</strong> ${status.connected_users || 0}</p>
                <p><strong>CPU:</strong> ${status.cpu_usage || '未知'}%</p>
                <p><strong>内存:</strong> ${status.memory_usage || '未知'} MB</p>
                <p><strong>更新时间:</strong> ${status.last_update || '未知'}</p>
            `;

            container.appendChild(card);
        });
    }

    // 更新单个发送端设备（增量更新）
    updateSingleSender(deviceId) {
        if (!this.senders[deviceId]) return;

        const device = this.senders[deviceId];
        const domain = device.room_server_domain || '未分配服务器';
        const categoryId = device.room_category_id || 0;
        const categoryName = this.getCategoryName(categoryId);

        console.log(`更新单个设备: ${deviceId}, 域名: ${domain}, 分类: ${categoryName}`);

        // 查找设备所在的域名组
        const domainGroup = document.querySelector(`[data-domain="${domain}"]`);
        if (!domainGroup) {
            console.warn(`未找到域名组: ${domain}，回退到完整更新`);
            // 如果域名组不存在，可能是新域名，需要完整更新
            this.updateSendersDisplay();
            return;
        }

        // 查找设备所在的分类组
        const categoryGroup = domainGroup.querySelector(`[data-category="${categoryName}"]`);
        if (!categoryGroup) {
            console.warn(`未找到分类组: ${categoryName}，回退到完整更新`);
            // 如果分类组不存在，可能是新分类，需要完整更新
            this.updateSendersDisplay();
            return;
        }

        // 查找现有的设备卡片
        const existingCard = categoryGroup.querySelector(`[data-device-id="${deviceId}"]`);

        // 保存当前活跃的视频连接状态
        const activeVideoConnections = new Map();
        if (this.videoConnections) {
            for (const [id, pc] of this.videoConnections.entries()) {
                if (pc.connectionState === 'connected' || pc.connectionState === 'connecting') {
                    activeVideoConnections.set(id, {
                        connectionState: pc.connectionState,
                        iceConnectionState: pc.iceConnectionState
                    });
                }
            }
        }

        // 创建新的设备卡片
        const newCard = this.createSenderCard(device);

        if (existingCard) {
            // 替换现有卡片
            existingCard.replaceWith(newCard);
        } else {
            // 添加新卡片到分类组
            const devicesContainer = categoryGroup.querySelector('.category-devices');
            if (devicesContainer) {
                devicesContainer.appendChild(newCard);
            }
        }

        // 更新域名统计
        this.updateDomainStats(domain);

        console.log(`设备 ${deviceId} 增量更新完成`);
    }

    // 更新单个接收端设备（增量更新）
    updateSingleReceiver(deviceId) {
        if (!this.receivers[deviceId]) return;

        const container = document.getElementById('receiversStatus');
        if (!container) return;

        const status = this.receivers[deviceId];

        // 查找现有的设备卡片
        const existingCard = container.querySelector(`[data-device-id="${deviceId}"]`);

        // 创建新的设备卡片
        const card = document.createElement('div');
        card.className = 'receiver-card';
        card.setAttribute('data-device-id', deviceId);

        const onlineStatus = status.online ?
            '<span class="status-online">● 在线</span>' :
            '<span class="status-offline">● 离线</span>';

        card.innerHTML = `
            <h3>${deviceId}</h3>
            <p><strong>状态:</strong> ${onlineStatus}</p>
            <p><strong>推流:</strong> ${status.streaming_status || '未知'}</p>
            <p><strong>服务:</strong> ${status.service_status || '未知'}</p>
            <p><strong>分辨率:</strong> ${status.resolution || '未知'}</p>
            <p><strong>码率:</strong> ${status.bitrate || '未知'} kbps</p>
            <p><strong>编码:</strong> ${status.codec || '未知'}</p>
            <p><strong>连接数:</strong> ${status.connected_users || 0}</p>
            <p><strong>CPU:</strong> ${status.cpu_usage || '未知'}%</p>
            <p><strong>内存:</strong> ${status.memory_usage || '未知'} MB</p>
            <p><strong>更新时间:</strong> ${status.last_update || '未知'}</p>
        `;

        if (existingCard) {
            // 替换现有卡片
            existingCard.replaceWith(card);
        } else {
            // 添加新卡片
            container.appendChild(card);
        }
    }



    // 更新域名统计信息
    updateDomainStats(domain) {
        const domainGroup = document.querySelector(`[data-domain="${domain}"]`);
        if (!domainGroup) return;

        const domainHeader = domainGroup.querySelector('.domain-group-header');
        if (!domainHeader) return;

        // 重新计算该域名下的统计信息
        const categories = this.groupedSenders[domain] || {};
        let domainOnlineCount = 0;
        let domainOfflineCount = 0;
        let totalDevices = 0;

        Object.values(categories).forEach(devices => {
            totalDevices += devices.length;
            devices.forEach(device => {
                if (device.online) {
                    domainOnlineCount++;
                } else {
                    domainOfflineCount++;
                }
            });
        });

        // 更新统计显示
        const onlineSpan = domainHeader.querySelector('.domain-stat-item.online .domain-stat-value');
        const offlineSpan = domainHeader.querySelector('.domain-stat-item.offline .domain-stat-value');
        const totalSpan = domainHeader.querySelector('.domain-stat-item.total .domain-stat-value');

        if (onlineSpan) onlineSpan.textContent = domainOnlineCount;
        if (offlineSpan) offlineSpan.textContent = domainOfflineCount;
        if (totalSpan) totalSpan.textContent = totalDevices;
    }

    // 更新发送端复选框
    updateSendersCheckbox() {
        const container = document.getElementById('sendersCheckbox');
        if (!container) return;

        container.innerHTML = '';

        Object.keys(this.senders).forEach(id => {
            const item = document.createElement('div');
            item.className = 'checkbox-item';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `sender_${id}`;
            checkbox.value = id;

            const label = document.createElement('label');
            label.htmlFor = `sender_${id}`;
            label.textContent = id;

            item.appendChild(checkbox);
            item.appendChild(label);
            container.appendChild(item);
        });
    }

    // 更新接收端复选框
    updateReceiversCheckbox() {
        const container = document.getElementById('receiversCheckbox');
        if (!container) return;

        container.innerHTML = '';

        Object.keys(this.receivers).forEach(id => {
            const item = document.createElement('div');
            item.className = 'checkbox-item';

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `receiver_${id}`;
            checkbox.value = id;

            const label = document.createElement('label');
            label.htmlFor = `receiver_${id}`;
            label.textContent = id;

            item.appendChild(checkbox);
            item.appendChild(label);
            container.appendChild(item);
        });
    }

    // 发送控制命令
    async sendCommand() {
        try {
            const targetSender = document.getElementById('targetSender').value.trim();
            const command = document.getElementById('controlCommand').value;
            const paramsText = document.getElementById('commandParams').value.trim();
            const deviceId = document.getElementById('deviceId').value.trim();

            if (!targetSender) {
                throw new Error('请输入目标发送端ID');
            }

            let params = {};
            if (paramsText) {
                params = JSON.parse(paramsText);
            }

            // 为截屏命令自动添加request_id和服务器地址
            if (command === 'take_screenshot' && !params.request_id) {
                params.request_id = `screenshot_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
                // 从设备信息中获取房间服务器域名
                const targetSender = document.getElementById('targetSender').value.trim();
                const senderInfo = this.getSenderInfo(targetSender);
                params.room_server_domain = senderInfo?.room_server_domain || window.location.origin;
            }

            const commandData = {
                command,
                params
            };

            if (deviceId) {
                commandData.device_id = deviceId;
            }

            const result = await this.apiRequest(`/commands/${targetSender}`, 'POST', commandData);
            this.log('命令', `${command} -> ${targetSender}: ${result.message}`, 'success');
        } catch (error) {
            this.log('命令', `发送命令失败: ${error.message}`, 'error');
        }
    }

    // 发送升级命令
    async sendUpgrade() {
        try {
            const apkUrl = document.getElementById('apkUrl').value.trim();
            const version = document.getElementById('version').value.trim();
            const forceUpgrade = document.getElementById('forceUpgrade').checked;
            const target = document.getElementById('upgradeTarget').value.trim();

            if (!apkUrl || !version) {
                throw new Error('请填写APK地址和版本号');
            }

            const upgradeData = {
                command: 'upgrade',
                params: {
                    apk_url: apkUrl,
                    version: version,
                    force: forceUpgrade
                }
            };

            if (target) {
                // 向特定发送端发送
                const result = await this.apiRequest(`/commands/${target}`, 'POST', upgradeData);
                this.log('升级', `升级命令已发送到 ${target}`, 'success');
            } else {
                // 向所有发送端发送
                const senders = Object.keys(this.senders);
                let successCount = 0;

                for (const senderId of senders) {
                    try {
                        await this.apiRequest(`/commands/${senderId}`, 'POST', upgradeData);
                        successCount++;
                    } catch (error) {
                        this.log('升级', `发送到 ${senderId} 失败: ${error.message}`, 'error');

                        // 如果错误信息表明设备不在线，自动更新设备状态为离线
                        if (error.message && error.message.includes('is not online')) {
                            this.log('设备状态', `检测到设备 ${senderId} 离线，更新状态`, 'warning');
                            this.updateDeviceStatus(senderId, 'sender', false);
                        }
                    }
                }

                this.log('升级', `升级命令已发送到 ${successCount}/${senders.length} 个发送端`, 'success');
            }
        } catch (error) {
            this.log('升级', `发送升级命令失败: ${error.message}`, 'error');
        }
    }

    // 向选中的发送端发送配置
    async sendConfigToSelected() {
        try {
            let selectedSenders = this.getSelectedSenders();
            // 读取手工输入
            const manualInput = document.getElementById('manualSenderIds');
            if (manualInput && manualInput.value.trim()) {
                const manualIds = manualInput.value.split(/\n|\r/).map(x => x.trim()).filter(x => x);
                selectedSenders = selectedSenders.concat(manualIds);
            }
            // 去重
            selectedSenders = Array.from(new Set(selectedSenders));
            if (selectedSenders.length === 0) {
                throw new Error('请选择或输入至少一个发送端');
            }
            const result = await this.apiRequest('/config/send', 'POST', {
                receiver_ids: selectedSenders
            });
            this.log('配置', `配置已发送到 ${selectedSenders.length} 个发送端`, 'success');
        } catch (error) {
            this.log('配置', `发送配置失败: ${error.message}`, 'error');
        }
    }

    // 批量发送命令
    async batchCommand() {
        try {
            let selectedSenders = this.getSelectedSenders();
            // 读取手工输入
            const manualInput = document.getElementById('manualSenderIds');
            if (manualInput && manualInput.value.trim()) {
                const manualIds = manualInput.value.split(/\n|\r/).map(x => x.trim()).filter(x => x);
                selectedSenders = selectedSenders.concat(manualIds);
            }
            // 去重
            selectedSenders = Array.from(new Set(selectedSenders));
            const command = document.getElementById('controlCommand').value;
            const paramsText = document.getElementById('commandParams').value.trim();
            if (selectedSenders.length === 0) {
                throw new Error('请选择或输入至少一个发送端');
            }
            let params = {};
            if (paramsText) {
                params = JSON.parse(paramsText);
            }
            // 读取间隔
            let interval = 0;
            const intervalInput = document.getElementById('batchInterval');
            if (intervalInput && intervalInput.value) {
                interval = parseInt(intervalInput.value, 10) || 0;
            }
            let successCount = 0;
            for (let i = 0; i < selectedSenders.length; i++) {
                const senderId = selectedSenders[i];
                try {
                    // 为每个设备创建独立的命令数据
                    let deviceParams = { ...params };

                    // 为截屏命令生成唯一的request_id和添加服务器地址
                    if (command === 'take_screenshot' && !deviceParams.request_id) {
                        deviceParams.request_id = `screenshot_${Date.now()}_${senderId}_${Math.random().toString(36).substring(2, 11)}`;
                        // 从设备信息中获取房间服务器域名
                        const senderInfo = this.getSenderInfo(senderId);
                        deviceParams.room_server_domain = senderInfo?.room_server_domain || window.location.origin;
                    }

                    const deviceCommandData = {
                        command,
                        params: deviceParams
                    };

                    await this.apiRequest(`/commands/${senderId}`, 'POST', deviceCommandData);
                    successCount++;
                } catch (error) {
                    this.log('批量命令', `发送到 ${senderId} 失败: ${error.message}`, 'error');

                    // 如果错误信息表明设备不在线，自动更新设备状态为离线
                    if (error.message && error.message.includes('is not online')) {
                        this.log('设备状态', `检测到设备 ${senderId} 离线，更新状态`, 'warning');
                        this.updateDeviceStatus(senderId, 'sender', false);
                    }
                }
                if (interval > 0 && i < selectedSenders.length - 1) {
                    await new Promise(res => setTimeout(res, interval * 1000));
                }
                // 休息后再打印日志
                this.log('批量命令', `${command} 已发送到 ${senderId}（第${i+1}/${selectedSenders.length}）`, 'success');
            }
            this.log('批量命令', `${command} 已发送到 ${successCount}/${selectedSenders.length} 个发送端`, 'success');
        } catch (error) {
            this.log('批量命令', `批量发送命令失败: ${error.message}`, 'error');
        }
    }

    // 快速发送命令
    async sendQuickCommand(targetId, command, params = {}) {
        try {
            // 为截屏命令自动添加request_id和服务器地址（如果没有提供）
            if (command === 'take_screenshot' && !params.request_id) {
                params.request_id = `screenshot_${Date.now()}_${targetId}_${Math.random().toString(36).substring(2, 11)}`;
                // 从设备信息中获取房间服务器域名
                const senderInfo = this.getSenderInfo(targetId);
                params.room_server_domain = senderInfo?.room_server_domain || window.location.origin;
            }

            const commandData = {
                command,
                params
            };

            const result = await this.apiRequest(`/commands/${targetId}`, 'POST', commandData);
            this.log('快速命令', `${command} -> ${targetId}: 发送成功`, 'success');
        } catch (error) {
            this.log('快速命令', `${command} -> ${targetId}: ${error.message}`, 'error');

            // 如果错误信息表明设备不在线，自动更新设备状态为离线
            if (error.message && error.message.includes('is not online')) {
                this.log('设备状态', `检测到设备 ${targetId} 离线，更新状态`, 'warning');
                this.updateDeviceStatus(targetId, 'sender', false);
            }
        }
    }

    // 获取选中的发送端
    getSelectedSenders() {
        const checkboxes = document.querySelectorAll('#sendersCheckbox input[type="checkbox"]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    // 获取选中的接收端
    getSelectedReceivers() {
        const checkboxes = document.querySelectorAll('#receiversCheckbox input[type="checkbox"]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }

    // 获取设备信息
    getSenderInfo(senderId) {
        try {
            // 从分组的发送端数据中查找
            for (const domain in this.groupedSenders) {
                for (const category in this.groupedSenders[domain]) {
                    const sender = this.groupedSenders[domain][category].find(s => s.sender_id === senderId);
                    if (sender) {
                        return sender;
                    }
                }
            }

            // 如果分组数据中没找到，从原始数据中查找
            const sendersArray = Array.isArray(this.senders) ? this.senders : Object.values(this.senders);
            return sendersArray.find(s => (s.sender_id || s.id) === senderId);
        } catch (error) {
            console.error('获取设备信息失败:', error);
            return null;
        }
    }

    // 在设备列表中显示截屏结果
    displayScreenshotResult(deviceId, fullUrl, requestId) {
        try {
            console.log('显示截屏结果:', { deviceId, fullUrl, requestId });

            // 查找设备卡片，尝试多种选择器
            let deviceCard = document.querySelector(`[data-sender-id="${deviceId}"]`);
            if (!deviceCard) {
                deviceCard = document.querySelector(`[data-device-id="${deviceId}"]`);
            }

            if (!deviceCard) {
                console.warn('未找到设备卡片:', deviceId);
                console.log('当前页面中的所有设备卡片:');
                const allCards = document.querySelectorAll('.sender-card');
                allCards.forEach(card => {
                    const senderId = card.getAttribute('data-sender-id');
                    const deviceIdAttr = card.getAttribute('data-device-id');
                    console.log(`  - sender-id: ${senderId}, device-id: ${deviceIdAttr}`);
                });
                return;
            }

            console.log('找到设备卡片:', deviceCard);

            // 验证设备卡片是否匹配
            const cardSenderId = deviceCard.getAttribute('data-sender-id');
            const cardDeviceId = deviceCard.getAttribute('data-device-id');
            console.log('卡片验证:', {
                targetDeviceId: deviceId,
                cardSenderId,
                cardDeviceId,
                match: cardSenderId === deviceId || cardDeviceId === deviceId
            });

            if (cardSenderId !== deviceId && cardDeviceId !== deviceId) {
                console.error('设备ID不匹配，拒绝显示截屏:', {
                    targetDeviceId: deviceId,
                    cardSenderId,
                    cardDeviceId
                });
                return;
            }

            // 查找媒体显示区域（已在卡片中预创建）
            let mediaArea = deviceCard.querySelector('.media-area');
            if (!mediaArea) {
                // 如果没有找到，创建一个并插入到卡片顶部
                mediaArea = document.createElement('div');
                mediaArea.className = 'media-area';
                deviceCard.insertBefore(mediaArea, deviceCard.firstChild);
            }

            // 显示媒体区域并设置样式
            mediaArea.style.display = 'block';
            mediaArea.style.cssText = `
                display: block;
                margin-bottom: 10px;
                padding: 8px;
                background: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #dee2e6;
            `;

            // 创建截屏预览
            const screenshotPreview = document.createElement('div');
            screenshotPreview.className = 'screenshot-preview';
            screenshotPreview.setAttribute('data-device-id', deviceId);
            screenshotPreview.setAttribute('data-request-id', requestId);
            const timestamp = new Date().toLocaleTimeString();

            console.log('创建截屏预览:', { deviceId, requestId, fullUrl, timestamp });

            screenshotPreview.innerHTML = `
                <div class="screenshot-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                    <span style="font-weight: 600; color: #28a745;">📸 截屏成功 - ${timestamp}</span>
                    <button onclick="this.closest('.screenshot-preview').remove()"
                            style="background: none; border: none; font-size: 18px; cursor: pointer; color: #6c757d;">×</button>
                </div>
                <div class="screenshot-image-container" style="text-align: center;">
                    <img src="${fullUrl}" alt="设备截屏 - ${deviceId}"
                         onclick="admin.showScreenshotModal('${fullUrl}', '${deviceId}')"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
                         style="max-width: 100%; height: auto; max-height: 200px; cursor: pointer; border-radius: 4px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);"
                         data-device-id="${deviceId}" data-image-url="${fullUrl}">
                    <div style="display: none; padding: 20px; background: #f8f9fa; border-radius: 4px; color: #6c757d;">
                        📷 图片加载失败<br>
                        <a href="${fullUrl}" target="_blank" style="color: #007bff;">点击查看原图</a>
                    </div>
                </div>
                <div class="screenshot-actions" style="margin-top: 8px; text-align: center;">
                    <a href="${fullUrl}" target="_blank" style="color: #007bff; text-decoration: none; margin-right: 15px;">
                        🔍 新窗口查看
                    </a>
                    <button onclick="admin.downloadScreenshot('${fullUrl}', '${deviceId}')"
                            style="background: none; border: none; color: #28a745; cursor: pointer;">
                        💾 下载图片
                    </button>
                </div>
            `;

            // 清除旧的截屏，添加新的
            const oldScreenshot = mediaArea.querySelector('.screenshot-preview');
            if (oldScreenshot) {
                oldScreenshot.remove();
            }
            mediaArea.appendChild(screenshotPreview);

            // 5秒后自动隐藏
            setTimeout(() => {
                if (screenshotContainer && screenshotContainer.parentNode) {
                    screenshotContainer.remove();
                }
            }, 30000); // 30秒后移除

        } catch (error) {
            console.error('显示截屏结果失败:', error);
        }
    }

    // 下载截屏图片
    downloadScreenshot(imageUrl, deviceId) {
        try {
            const a = document.createElement('a');
            a.href = imageUrl;
            a.download = `${deviceId}_screenshot_${Date.now()}.png`;
            a.target = '_blank';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            this.log('截屏下载', `正在下载 ${deviceId} 的截屏图片`, 'info');
        } catch (error) {
            console.error('下载截屏失败:', error);
            this.log('截屏下载', `下载失败: ${error.message}`, 'error');
        }
    }

    // 在设备列表中显示日志下载结果
    displayLogDownloadResult(deviceId, logUrl, filename) {
        try {
            // 查找设备卡片
            const deviceCard = document.querySelector(`[data-sender-id="${deviceId}"]`);
            if (!deviceCard) {
                console.warn('未找到设备卡片:', deviceId);
                return;
            }

            // 查找媒体显示区域
            let mediaArea = deviceCard.querySelector('.media-area');
            if (!mediaArea) {
                mediaArea = document.createElement('div');
                mediaArea.className = 'media-area';
                deviceCard.insertBefore(mediaArea, deviceCard.firstChild);
            }

            // 显示媒体区域
            mediaArea.style.display = 'block';
            mediaArea.style.cssText = `
                display: block;
                margin-bottom: 10px;
                padding: 8px;
                background: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #dee2e6;
            `;

            // 创建日志下载结果容器
            const logContainer = document.createElement('div');
            logContainer.className = 'log-download-result';
            logContainer.style.cssText = `
                margin-bottom: 10px;
                padding: 8px;
                background: #e3f2fd;
                border-radius: 6px;
                border-left: 4px solid #2196f3;
            `;

            // 清除旧的日志通知，添加新的
            const oldLog = mediaArea.querySelector('.log-download-result');
            if (oldLog) {
                oldLog.remove();
            }
            mediaArea.appendChild(logContainer);

            // 创建日志下载链接
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <span style="color: #2196f3; font-weight: 600;">📄 日志下载成功</span>
                    <span style="color: #6c757d; font-size: 0.9em;">${timestamp}</span>
                </div>
                <div style="margin-top: 4px;">
                    <a href="${logUrl}" target="_blank"
                       style="color: #007bff; text-decoration: none; font-size: 0.9em; margin-right: 10px;">
                        📂 打开日志文件
                    </a>
                    <span style="color: #6c757d; font-size: 0.8em;">${filename}</span>
                </div>
            `;

            // 10秒后自动隐藏
            setTimeout(() => {
                if (logContainer && logContainer.parentNode) {
                    logContainer.remove();
                }
            }, 10000);

        } catch (error) {
            console.error('显示日志下载结果失败:', error);
        }
    }

    // 显示截屏模态框
    showScreenshotModal(imageUrl, deviceId) {
        console.log('显示截屏模态框:', { imageUrl, deviceId });

        // 验证参数
        if (!imageUrl || !deviceId) {
            console.error('截屏模态框参数无效:', { imageUrl, deviceId });
            return;
        }

        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            cursor: pointer;
        `;

        modal.innerHTML = `
            <div style="position: relative; max-width: 90%; max-height: 90%;">
                <img src="${imageUrl}"
                     style="max-width: 100%; max-height: 100%; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.3);"
                     alt="设备截屏 - ${deviceId}"
                     onload="console.log('截屏模态框图片加载成功:', '${imageUrl}')"
                     onerror="console.error('截屏模态框图片加载失败:', '${imageUrl}')">
                <div style="position: absolute; top: -40px; right: 0; color: white; font-size: 14px; background: rgba(0,0,0,0.5); padding: 4px 8px; border-radius: 4px;">
                    设备: ${deviceId} | 点击任意处关闭
                </div>
            </div>
        `;

        modal.onclick = () => {
            console.log('关闭截屏模态框');
            modal.remove();
        };
        document.body.appendChild(modal);
    }

    // 显示视频流预览
    showVideoPreview(deviceId) {
        try {
            console.log('开始显示视频预览:', deviceId);

            // 查找设备卡片
            const deviceCard = document.querySelector(`[data-sender-id="${deviceId}"]`);
            if (!deviceCard) {
                console.error('未找到设备卡片:', deviceId);
                this.showToast('error', '错误', '未找到设备卡片');
                return;
            }

            console.log('找到设备卡片:', deviceCard);

            // 查找媒体显示区域（已在卡片中预创建）
            let mediaArea = deviceCard.querySelector('.media-area');
            if (!mediaArea) {
                // 如果没有找到，创建一个并插入到卡片顶部
                mediaArea = document.createElement('div');
                mediaArea.className = 'media-area';
                deviceCard.insertBefore(mediaArea, deviceCard.firstChild);
            }

            // 显示媒体区域并设置简洁样式
            mediaArea.style.display = 'block';
            mediaArea.style.cssText = `
                display: block;
                margin-bottom: 10px;
                padding: 4px;
                background: transparent;
                border-radius: 4px;
            `;

            // 检查是否已有视频预览
            let videoPreview = mediaArea.querySelector('.video-preview');
            if (videoPreview) {
                // 如果已有预览，则关闭
                videoPreview.remove();
                this.log('视频预览', `已关闭 ${deviceId} 的视频预览`, 'info');
                return;
            }

            // 创建简洁的视频预览容器
            videoPreview = document.createElement('div');
            videoPreview.className = 'video-preview';
            videoPreview.innerHTML = `
                <div class="video-container" style="position: relative; background: #000; border-radius: 4px; overflow: hidden;">
                    <video id="video-${deviceId}" autoplay muted playsinline
                           style="width: 100%; height: 180px; object-fit: contain; background: #000;">
                    </video>
                    <div class="video-status" style="position: absolute; top: 4px; left: 4px; background: rgba(0,0,0,0.7); color: white; padding: 2px 6px; border-radius: 3px; font-size: 11px;">
                        连接中...
                    </div>
                    <button onclick="admin.closeVideoPreview('${deviceId}')"
                            style="position: absolute; top: 4px; right: 4px; background: rgba(0,0,0,0.7); color: white; border: none; width: 24px; height: 24px; border-radius: 3px; cursor: pointer; font-size: 14px;">×</button>
                </div>
            `;

            mediaArea.appendChild(videoPreview);

            // 启动WebRTC连接
            console.log('准备启动WebRTC连接...');
            this.startVideoStream(deviceId);
            this.log('视频预览', `正在启动 ${deviceId} 的视频预览`, 'info');

            // 显示成功提示
            this.showToast('info', '视频预览', `正在连接 ${deviceId} 的视频流...`);

        } catch (error) {
            this.log('视频预览', `启动视频预览失败: ${error.message}`, 'error');
        }
    }

    // 启动视频流连接
    async startVideoStream(deviceId) {
        try {
            console.log('启动视频流连接:', deviceId);

            const video = document.getElementById(`video-${deviceId}`);
            const statusEl = video?.parentNode?.querySelector('.video-status');

            if (!video) {
                console.error('视频元素未找到:', `video-${deviceId}`);
                throw new Error('视频元素未找到');
            }

            console.log('找到视频元素:', video);

            // 创建WebRTC连接 - 使用与mobile_receiver.html相同的配置
            const pc = new RTCPeerConnection({
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:8.134.131.24:3478' }
                ],
                bundlePolicy: 'max-bundle',
                rtcpMuxPolicy: 'require'
            });

            // 创建媒体流，与mobile_receiver.html保持一致的格式
            const mediaStream = new MediaStream();
            const streamId = `${Math.random().toString(36).substring(2, 10)}-${Math.random().toString(36).substring(2, 6)}-${Math.random().toString(36).substring(2, 6)}-${Math.random().toString(36).substring(2, 6)}-${Math.random().toString(36).substring(2, 14)}`;

            // 添加视频收发器，使用完整的媒体流
            const videoTransceiver = pc.addTransceiver('video', {
                direction: 'sendrecv',
                streams: [mediaStream]
            });

            // 添加音频收发器
            const audioTransceiver = pc.addTransceiver('audio', {
                direction: 'sendrecv',
                streams: [mediaStream]
            });

            console.log('已创建媒体流ID:', streamId);

            console.log('WebRTC连接已创建:', pc);

            // 处理远程流
            pc.ontrack = (event) => {
                console.log('收到远程视频流:', event);
                video.srcObject = event.streams[0];
                if (statusEl) {
                    statusEl.textContent = '已连接';
                    statusEl.style.background = 'rgba(40, 167, 69, 0.8)';
                }
            };

            // 处理ICE候选
            pc.onicecandidate = (event) => {
                console.log('ICE候选事件:', event);
                if (event.candidate) {
                    console.log('发送ICE候选:', event.candidate);
                    // 发送ICE候选到信令服务器
                    this.sendSignalingMessage(deviceId, {
                        type: 'candidate',
                        candidate: event.candidate
                    });
                } else {
                    console.log('ICE候选收集完成');
                }
            };

            // 处理连接状态变化
            pc.onconnectionstatechange = () => {
                const state = pc.connectionState;
                console.log(`WebRTC连接状态变化 ${deviceId}: ${state}`);

                if (statusEl) {
                    statusEl.textContent = this.getConnectionStateText(state);

                    if (state === 'connected') {
                        statusEl.style.background = 'rgba(40, 167, 69, 0.8)';
                        console.log(`设备 ${deviceId} WebRTC连接已建立`);
                    } else if (state === 'failed' || state === 'disconnected') {
                        statusEl.style.background = 'rgba(220, 53, 69, 0.8)';
                        console.log(`设备 ${deviceId} WebRTC连接失败或断开: ${state}`);

                        // 只在真正失败时重连，避免频繁重连
                        if (state === 'failed') {
                            // 自动添加视频预览失败备注
                            this.updateDeviceRemark(deviceId, '视频预览失败');

                            // 检查是否已经有重连计划，避免重复重连
                            if (!this.reconnectTimers) {
                                this.reconnectTimers = new Map();
                            }

                            if (this.reconnectTimers.has(deviceId)) {
                                console.log(`设备 ${deviceId} 已有重连计划，跳过`);
                                return;
                            }

                            console.log(`计划在5秒后重连设备 ${deviceId}`);
                            const timerId = setTimeout(() => {
                                this.reconnectTimers.delete(deviceId);
                                this.reconnectVideo(deviceId);
                            }, 5000); // 延长重连间隔到5秒

                            this.reconnectTimers.set(deviceId, timerId);
                        }
                    } else {
                        statusEl.style.background = 'rgba(255, 193, 7, 0.8)';
                    }
                }
            };

            // 创建offer - 使用与mobile_receiver.html相同的参数
            console.log('开始创建offer...');
            const offer = await pc.createOffer({
                offerToReceiveVideo: true,
                offerToReceiveAudio: true,
                iceRestart: true  // 添加iceRestart参数，与mobile_receiver.html一致
            });
            console.log('offer创建成功:', offer);

            await pc.setLocalDescription(offer);
            console.log('本地描述设置成功');

            // 修改SDP以完全匹配mobile_receiver.html的成功格式
            let modifiedSdp = offer.sdp;

            // 1. 重新排列编解码器优先级，H.264优先（与mobile_receiver.html一致）
            modifiedSdp = modifiedSdp.replace(
                /m=video 9 UDP\/TLS\/RTP\/SAVPF [0-9 ]+/,
                'm=video 9 UDP/TLS/RTP/SAVPF 102 103 104 105 106 107 108 109 127 125 39 40 112 113 96 97 98 99 100 101 45 46 116 117 118'
            );

            // 2. 修复媒体流ID格式，移除"-"前缀
            modifiedSdp = modifiedSdp.replace(/a=msid:- ([a-f0-9-]+)/, `a=msid:${streamId} $1`);

            // 3. 添加完整的msid-semantic
            modifiedSdp = modifiedSdp.replace('a=msid-semantic: WMS', `a=msid-semantic: WMS ${streamId}`);

            console.log('SDP已修改：H.264优先 + 完整媒体流ID');
            console.log('媒体流ID:', streamId);

            // 发送offer到设备
            console.log('发送offer到设备:', deviceId);
            this.sendSignalingMessage(deviceId, {
                type: 'offer',
                sdp: modifiedSdp
            });

            // 存储连接以便后续使用
            if (!this.videoConnections) {
                this.videoConnections = new Map();
            }
            this.videoConnections.set(deviceId, pc);
            console.log('WebRTC连接已存储:', deviceId);

        } catch (error) {
            console.error('启动视频流失败:', error);
            const statusEl = document.querySelector(`#video-${deviceId}`)?.parentNode?.querySelector('.video-status');
            if (statusEl) {
                statusEl.textContent = '连接失败';
                statusEl.style.background = 'rgba(220, 53, 69, 0.8)';
            }
        }
    }

    // 发送信令消息
    sendSignalingMessage(deviceId, message) {
        try {
            console.log('发送WebRTC信令:', { deviceId, message });

            if (adminWS.ws && adminWS.ws.readyState === WebSocket.OPEN) {
                // 直接发送offer/answer/ice-candidate消息，与mobile_receiver.html保持一致
                const signalingData = {
                    type: message.type,  // 'offer', 'answer', 'ice-candidate'
                    target: deviceId,
                    timestamp: Date.now()
                };

                if (message.type === 'offer' || message.type === 'answer') {
                    signalingData.sdp = message.sdp;
                } else if (message.type === 'candidate') {
                    signalingData.candidate = message.candidate;
                }

                adminWS.send(signalingData);
                console.log('WebRTC信令已发送:', signalingData);
            } else {
                console.error('WebSocket未连接，无法发送信令');
                throw new Error('WebSocket连接不可用');
            }
        } catch (error) {
            console.error('发送WebRTC信令失败:', error);
        }
    }

    // 获取连接状态文本
    getConnectionStateText(state) {
        const stateMap = {
            'new': '初始化',
            'connecting': '连接中',
            'connected': '已连接',
            'disconnected': '已断开',
            'failed': '连接失败',
            'closed': '已关闭'
        };
        return stateMap[state] || state;
    }

    // 重连视频流
    reconnectVideo(deviceId) {
        try {
            console.log(`开始重连视频流: ${deviceId}`);

            // 清理重连定时器
            if (this.reconnectTimers && this.reconnectTimers.has(deviceId)) {
                clearTimeout(this.reconnectTimers.get(deviceId));
                this.reconnectTimers.delete(deviceId);
            }

            // 检查设备是否在线
            const device = this.senders[deviceId];
            if (!device || !device.online) {
                console.log(`设备 ${deviceId} 不在线，取消重连`);
                return;
            }

            // 检查是否有视频预览界面
            const videoElement = document.getElementById(`video-${deviceId}`);
            if (!videoElement) {
                console.log(`设备 ${deviceId} 的视频预览界面不存在，取消重连`);
                return;
            }

            // 关闭旧连接
            if (this.videoConnections && this.videoConnections.has(deviceId)) {
                const oldPc = this.videoConnections.get(deviceId);
                try {
                    oldPc.close();
                } catch (e) {
                    console.error('关闭旧连接失败:', e);
                }
                this.videoConnections.delete(deviceId);
            }

            // 更新状态显示
            const statusEl = videoElement.parentNode?.querySelector('.video-status');
            if (statusEl) {
                statusEl.textContent = '重新连接中...';
                statusEl.style.background = 'rgba(255, 193, 7, 0.8)';
            }

            // 重新启动视频流
            setTimeout(() => {
                this.startVideoStream(deviceId);
            }, 1000);

        } catch (error) {
            console.error(`重连视频流失败 ${deviceId}:`, error);
        }
    }

    // 处理备注更新响应
    handleRemarkUpdated(data) {
        try {
            console.log('备注更新响应:', data);

            if (data.success) {
                console.log(`设备 ${data.device_id} 备注更新成功: ${data.remark}`);

                // 更新本地数据
                if (this.senders[data.device_id]) {
                    this.senders[data.device_id].remark = data.remark;
                }
            } else {
                console.error(`设备 ${data.device_id} 备注更新失败: ${data.message}`);
                this.showToast('error', '备注更新失败', data.message || '未知错误');
            }
        } catch (error) {
            console.error('处理备注更新响应失败:', error);
        }
    }

    // 更新设备备注
    async updateDeviceRemark(deviceId, remark) {
        try {
            console.log(`更新设备备注: ${deviceId} -> ${remark}`);

            // 发送WebSocket消息更新备注
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({
                    type: 'update_remark',
                    device_id: deviceId,
                    remark: remark
                }));

                console.log(`备注更新请求已发送: ${deviceId}`);
            } else {
                console.error('WebSocket连接未建立，无法更新备注');
            }
        } catch (error) {
            console.error('更新设备备注失败:', error);
        }
    }

    // 切换视频预览
    toggleVideoPreview(deviceId) {
        const videoElement = document.getElementById(`video-${deviceId}`);
        const button = document.getElementById(`video-btn-${deviceId}`);

        if (videoElement && videoElement.style.display !== 'none') {
            // 当前正在预览，关闭预览
            this.closeVideoPreview(deviceId);
            if (button) {
                button.innerHTML = '📺 视频预览';
            }
        } else {
            // 当前未预览，开始预览
            this.showVideoPreview(deviceId);
            if (button) {
                button.innerHTML = '❌ 关闭预览';
            }
        }
    }

    // 关闭视频预览
    closeVideoPreview(deviceId) {
        try {
            console.log(`关闭视频预览: ${deviceId}`);

            // 关闭WebRTC连接
            if (this.videoConnections && this.videoConnections.has(deviceId)) {
                const pc = this.videoConnections.get(deviceId);
                pc.close();
                this.videoConnections.delete(deviceId);
                console.log(`WebRTC连接已关闭: ${deviceId}`);
            }

            // 隐藏视频元素
            const videoElement = document.getElementById(`video-${deviceId}`);
            if (videoElement) {
                videoElement.style.display = 'none';
                videoElement.srcObject = null;
            }

            // 隐藏媒体区域
            const deviceCard = document.querySelector(`[data-sender-id="${deviceId}"]`);
            if (deviceCard) {
                const mediaArea = deviceCard.querySelector('.media-area');
                if (mediaArea) {
                    mediaArea.style.display = 'none';
                    mediaArea.innerHTML = '';
                }
            }

            // 更新按钮状态
            const button = document.getElementById(`video-btn-${deviceId}`);
            if (button) {
                button.innerHTML = '📺 视频预览';
            }

        } catch (error) {
            console.error(`关闭视频预览失败 ${deviceId}:`, error);
        }
    }

    // 切换视频播放/暂停
    toggleVideoPlayback(deviceId) {
        const video = document.getElementById(`video-${deviceId}`);
        if (video) {
            if (video.paused) {
                video.play();
            } else {
                video.pause();
            }
        }
    }

    // 视频截图
    takeVideoScreenshot(deviceId) {
        const video = document.getElementById(`video-${deviceId}`);
        if (video && video.videoWidth > 0) {
            const canvas = document.createElement('canvas');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(video, 0, 0);

            // 转换为blob并下载
            canvas.toBlob((blob) => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${deviceId}_screenshot_${Date.now()}.png`;
                a.click();
                URL.revokeObjectURL(url);
            });

            this.log('视频截图', `已截取 ${deviceId} 的视频画面`, 'success');
        }
    }

    // 切换全屏
    toggleVideoFullscreen(deviceId) {
        const video = document.getElementById(`video-${deviceId}`);
        if (video) {
            if (document.fullscreenElement) {
                document.exitFullscreen();
            } else {
                video.requestFullscreen().catch(err => {
                    console.error('全屏失败:', err);
                });
            }
        }
    }

    // 批量视频预览
    batchVideoPreview(domain) {
        try {
            const onlineDevices = this.getOnlineDevicesByDomain(domain);

            if (onlineDevices.length === 0) {
                this.showToast('warning', '批量视频预览', `域名 ${domain} 下没有在线设备`);
                return;
            }

            const confirmMsg = `确定要为域名 ${domain} 下的 ${onlineDevices.length} 个在线设备开启视频预览吗？`;
            if (!confirm(confirmMsg)) {
                return;
            }

            this.log('批量操作', `开始批量视频预览: ${domain} (${onlineDevices.length} 个设备)`, 'info');

            let successCount = 0;
            onlineDevices.forEach((deviceId, index) => {
                setTimeout(() => {
                    try {
                        this.showVideoPreview(deviceId);
                        successCount++;
                        this.log('批量视频预览', `${deviceId}: 已启动`, 'success');
                    } catch (error) {
                        this.log('批量视频预览', `${deviceId}: 启动失败 - ${error.message}`, 'error');
                    }
                }, index * 500); // 每个设备间隔500ms，避免同时创建太多连接
            });

            this.showToast('info', '批量视频预览', `正在为 ${onlineDevices.length} 个设备启动视频预览...`);

        } catch (error) {
            this.log('批量视频预览', `批量操作失败: ${error.message}`, 'error');
        }
    }

    // 批量截屏
    batchScreenshot(domain) {
        try {
            const onlineDevices = this.getOnlineDevicesByDomain(domain);

            if (onlineDevices.length === 0) {
                this.showToast('warning', '批量截屏', `域名 ${domain} 下没有在线设备`);
                return;
            }

            const confirmMsg = `确定要为域名 ${domain} 下的 ${onlineDevices.length} 个在线设备执行截屏吗？`;
            if (!confirm(confirmMsg)) {
                return;
            }

            this.log('批量操作', `开始批量截屏: ${domain} (${onlineDevices.length} 个设备)`, 'info');
            console.log('批量截屏设备列表:', onlineDevices);

            let successCount = 0;
            onlineDevices.forEach((deviceId, index) => {
                setTimeout(() => {
                    try {
                        console.log(`发送截屏命令到设备: ${deviceId} (索引: ${index})`);
                        this.sendQuickCommand(deviceId, 'take_screenshot');
                        successCount++;
                        this.log('批量截屏', `${deviceId}: 命令已发送`, 'success');
                    } catch (error) {
                        this.log('批量截屏', `${deviceId}: 发送失败 - ${error.message}`, 'error');
                    }
                }, index * 500); // 增加间隔到500ms，避免并发冲突
            });

            this.showToast('info', '批量截屏', `已向 ${onlineDevices.length} 个设备发送截屏命令`);

        } catch (error) {
            this.log('批量截屏', `批量操作失败: ${error.message}`, 'error');
        }
    }

    // 批量视频参数设置
    batchVideoSettings(domain) {
        try {
            const onlineDevices = this.getOnlineDevicesByDomain(domain);

            if (onlineDevices.length === 0) {
                this.showToast('warning', '批量视频设置', `域名 ${domain} 下没有在线设备`);
                return;
            }

            // 显示批量视频设置对话框
            const content = `
                <div class="form-row">
                    <div class="form-col">
                        <label for="batchVideoResolution">分辨率:</label>
                        <select id="batchVideoResolution">
                            <option value="1920x1080">1920x1080 (1080p)</option>
                            <option value="1280x720">1280x720 (720p)</option>
                            <option value="1600x900">1600x900</option>
                            <option value="1366x768">1366x768</option>
                            <option value="custom">自定义</option>
                        </select>
                    </div>
                    <div class="form-col">
                        <label for="batchVideoBitrate">码率 (kbps):</label>
                        <input type="number" id="batchVideoBitrate" value="3000" min="500" max="10000">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <label for="batchVideoFramerate">帧率 (fps):</label>
                        <input type="number" id="batchVideoFramerate" value="60" min="15" max="120">
                    </div>
                    <div class="form-col">
                        <label>目标设备数量: <strong>${onlineDevices.length}</strong></label>
                    </div>
                </div>
            `;

            this.currentCommand = () => {
                const resolution = document.getElementById('batchVideoResolution').value;
                const bitrate = document.getElementById('batchVideoBitrate').value;
                const framerate = document.getElementById('batchVideoFramerate').value;

                const confirmMsg = `确定要为域名 ${domain} 下的 ${onlineDevices.length} 个设备设置视频参数吗？\n分辨率: ${resolution}\n码率: ${bitrate}kbps\n帧率: ${framerate}fps`;
                if (!confirm(confirmMsg)) {
                    return false;
                }

                this.log('批量操作', `开始批量视频设置: ${domain} (${onlineDevices.length} 个设备)`, 'info');

                let successCount = 0;
                onlineDevices.forEach((deviceId, index) => {
                    setTimeout(() => {
                        try {
                            this.sendQuickCommand(deviceId, 'set_video_params', {
                                resolution: resolution,
                                bitrate: parseInt(bitrate),
                                framerate: parseInt(framerate)
                            });
                            successCount++;
                            this.log('批量视频设置', `${deviceId}: 命令已发送`, 'success');
                        } catch (error) {
                            this.log('批量视频设置', `${deviceId}: 发送失败 - ${error.message}`, 'error');
                        }
                    }, index * 200);
                });

                this.showToast('info', '批量视频设置', `已向 ${onlineDevices.length} 个设备发送视频设置命令`);
                return true;
            };

            this.showModal('🎥 批量视频参数设置', content, this.currentCommand);

        } catch (error) {
            this.log('批量视频设置', `批量操作失败: ${error.message}`, 'error');
        }
    }

    // 批量游戏设置
    batchGameSettings(domain) {
        try {
            const onlineDevices = this.getOnlineDevicesByDomain(domain);

            if (onlineDevices.length === 0) {
                this.showToast('warning', '批量游戏设置', `域名 ${domain} 下没有在线设备`);
                return;
            }

            // 显示批量游戏设置对话框
            const content = `
                <div class="checkbox-wrapper">
                    <input type="checkbox" id="batchGameEnabled" checked>
                    <label for="batchGameEnabled">启用游戏功能</label>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <label for="batchGamePackage">游戏包名:</label>
                        <input type="text" id="batchGamePackage" placeholder="com.example.game">
                    </div>
                    <div class="form-col">
                        <label for="batchGameActivity">游戏Activity:</label>
                        <input type="text" id="batchGameActivity" placeholder="MainActivity">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-col">
                        <label for="batchAutoStart">自动启动:</label>
                        <select id="batchAutoStart">
                            <option value="true">是</option>
                            <option value="false">否</option>
                        </select>
                    </div>
                    <div class="form-col">
                        <label>目标设备数量: <strong>${onlineDevices.length}</strong></label>
                    </div>
                </div>
            `;

            this.currentCommand = () => {
                const gameEnabled = document.getElementById('batchGameEnabled').checked;
                const gamePackage = document.getElementById('batchGamePackage').value;
                const gameActivity = document.getElementById('batchGameActivity').value;
                const autoStart = document.getElementById('batchAutoStart').value === 'true';

                const confirmMsg = `确定要为域名 ${domain} 下的 ${onlineDevices.length} 个设备设置游戏参数吗？\n启用游戏: ${gameEnabled ? '是' : '否'}\n游戏包名: ${gamePackage}\n自动启动: ${autoStart ? '是' : '否'}`;
                if (!confirm(confirmMsg)) {
                    return false;
                }

                this.log('批量操作', `开始批量游戏设置: ${domain} (${onlineDevices.length} 个设备)`, 'info');

                let successCount = 0;
                onlineDevices.forEach((deviceId, index) => {
                    setTimeout(() => {
                        try {
                            this.sendQuickCommand(deviceId, 'set_game_config', {
                                enabled: gameEnabled,
                                package_name: gamePackage,
                                activity_name: gameActivity,
                                auto_start: autoStart
                            });
                            successCount++;
                            this.log('批量游戏设置', `${deviceId}: 命令已发送`, 'success');
                        } catch (error) {
                            this.log('批量游戏设置', `${deviceId}: 发送失败 - ${error.message}`, 'error');
                        }
                    }, index * 200);
                });

                this.showToast('info', '批量游戏设置', `已向 ${onlineDevices.length} 个设备发送游戏设置命令`);
                return true;
            };

            this.showModal('🎮 批量游戏设置', content, this.currentCommand);

        } catch (error) {
            this.log('批量游戏设置', `批量操作失败: ${error.message}`, 'error');
        }
    }

    // 批量下载日志
    batchDownloadLogs(domain) {
        try {
            const onlineDevices = this.getOnlineDevicesByDomain(domain);

            if (onlineDevices.length === 0) {
                this.showToast('warning', '批量下载日志', `域名 ${domain} 下没有在线设备`);
                return;
            }

            const confirmMsg = `确定要为域名 ${domain} 下的 ${onlineDevices.length} 个在线设备下载日志吗？`;
            if (!confirm(confirmMsg)) {
                return;
            }

            this.log('批量操作', `开始批量下载日志: ${domain} (${onlineDevices.length} 个设备)`, 'info');

            let successCount = 0;
            onlineDevices.forEach((deviceId, index) => {
                setTimeout(() => {
                    try {
                        this.sendQuickCommand(deviceId, 'download_logs', {method: 'ftp'});
                        successCount++;
                        this.log('批量下载日志', `${deviceId}: 命令已发送`, 'success');
                    } catch (error) {
                        this.log('批量下载日志', `${deviceId}: 发送失败 - ${error.message}`, 'error');
                    }
                }, index * 300); // 每个设备间隔300ms
            });

            this.showToast('info', '批量下载日志', `已向 ${onlineDevices.length} 个设备发送下载日志命令`);

        } catch (error) {
            this.log('批量下载日志', `批量操作失败: ${error.message}`, 'error');
        }
    }

    // 批量游戏测试
    batchGameTest(domain) {
        try {
            const onlineDevices = this.getOnlineDevicesByDomain(domain);

            if (onlineDevices.length === 0) {
                this.showToast('warning', '批量游戏测试', `域名 ${domain} 下没有在线设备`);
                return;
            }

            const confirmMsg = `确定要为域名 ${domain} 下的 ${onlineDevices.length} 个在线设备启动游戏测试吗？`;
            if (!confirm(confirmMsg)) {
                return;
            }

            this.log('批量游戏测试', `开始为域名 ${domain} 下的 ${onlineDevices.length} 个设备启动游戏测试`, 'info');

            // 并行启动所有设备的测试
            onlineDevices.forEach(deviceId => {
                this.startGameTest(deviceId);
            });

            this.showToast('info', '批量游戏测试', `已为 ${onlineDevices.length} 个设备启动游戏测试`);

        } catch (error) {
            this.log('批量游戏测试', `批量操作失败: ${error.message}`, 'error');
        }
    }

    // 启动单个设备的游戏测试
    async startGameTest(deviceId) {
        try {
            // 检查设备是否在线
            const device = this.senders[deviceId];
            if (!device || !device.online) {
                this.updateDeviceTestStatus(deviceId, 'error', '设备离线');
                return;
            }

            this.log('游戏测试', `开始测试设备 ${deviceId}`, 'info');
            this.updateDeviceTestStatus(deviceId, 'testing', '正在连接...');

            // 获取房间服务器域名
            const roomServerDomain = this.getRoomServerDomain(device);
            if (!roomServerDomain) {
                throw new Error('设备房间服务器域名未配置');
            }

            // 创建游戏测试实例
            const gameTest = new GameTest(deviceId, device.gameSn || this.generateGameSn(), roomServerDomain);
            this.gameTests = this.gameTests || new Map();
            this.gameTests.set(deviceId, gameTest);

            // 启动测试
            await gameTest.start();

        } catch (error) {
            this.log('游戏测试', `设备 ${deviceId} 测试启动失败: ${error.message}`, 'error');
            this.updateDeviceTestStatus(deviceId, 'error', error.message);
        }
    }

    // 停止游戏测试
    stopGameTest(deviceId) {
        const gameTest = this.gameTests?.get(deviceId);
        if (gameTest) {
            gameTest.stop();
            this.gameTests.delete(deviceId);
            this.updateDeviceTestStatus(deviceId, 'stopped', '测试已停止');
        }
    }

    // 更新设备测试状态
    updateDeviceTestStatus(deviceId, status, message) {
        const deviceCard = document.querySelector(`[data-device-id="${deviceId}"]`);
        if (!deviceCard) return;

        let testStatusElement = deviceCard.querySelector('.test-status');
        if (!testStatusElement) {
            // 创建测试状态显示元素
            testStatusElement = document.createElement('div');
            testStatusElement.className = 'test-status';
            testStatusElement.style.fontSize = '12px';
            testStatusElement.style.marginTop = '4px';
            const statusElement = deviceCard.querySelector('.device-status');
            if (statusElement) {
                statusElement.appendChild(testStatusElement);
            }
        }

        // 更新状态显示
        const statusColors = {
            'testing': '#2196F3',
            'success': '#4CAF50',
            'error': '#F44336',
            'warning': '#FF9800',
            'stopped': '#9E9E9E'
        };

        testStatusElement.style.color = statusColors[status] || '#666';
        testStatusElement.textContent = `🎯 ${message}`;

        // 如果是错误状态，记录到日志中
        if (status === 'error') {
            this.log('游戏测试', `设备 ${deviceId}: ${message}`, 'error');
        }
    }

    // 生成游戏序列号
    generateGameSn() {
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }

    // 获取设备的房间服务器域名
    getRoomServerDomain(device) {
        // 尝试多种方式获取房间服务器域名
        if (device.room_server_domain) {
            return device.room_server_domain;
        }

        // 如果设备没有配置，尝试从全局配置获取
        if (device.domain) {
            // 假设房间服务器域名格式为: http://domain:port
            return `http://${device.domain}:8080`;
        }

        // 从设备IP构建默认房间服务器地址
        if (device.ip) {
            return `http://${device.ip}:8080`;
        }

        // 尝试从设备ID中提取IP
        if (device.id && typeof device.id === 'string' && device.id.includes('-')) {
            const parts = device.id.split('-');
            if (parts.length > 0 && this.isValidIP(parts[0])) {
                return `http://${parts[0]}:8080`;
            }
        }

        return null;
    }

    // 验证IP地址格式
    isValidIP(ip) {
        const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
        if (!ipRegex.test(ip)) return false;

        const parts = ip.split('.');
        return parts.every(part => {
            const num = parseInt(part, 10);
            return num >= 0 && num <= 255;
        });
    }

    // 获取指定域名下的在线设备
    getOnlineDevicesByDomain(domain) {
        const onlineDevices = [];

        if (this.groupedSenders[domain]) {
            Object.values(this.groupedSenders[domain]).forEach(devices => {
                devices.forEach(device => {
                    if (device.online) {
                        onlineDevices.push(device.sender_id || device.id);
                    }
                });
            });
        }

        return onlineDevices;
    }

    // 生成下拉菜单内容（3栏布局）
    generateDropdownMenu(deviceId, status) {
        return `
            <div class="dropdown-columns">
                <div class="dropdown-section">
                    <h4>服务控制</h4>
                    <button class="dropdown-btn btn-success" onclick="admin.sendQuickCommand('${deviceId}', 'start_service')">
                        🚀 启动服务
                    </button>
                    <button class="dropdown-btn btn-warning" onclick="admin.sendQuickCommand('${deviceId}', 'stop_service')">
                        ⏹️ 停止服务
                    </button>
                    <button class="dropdown-btn btn-info" onclick="admin.sendQuickCommand('${deviceId}', 'restart_service')">
                        🔄 重启服务
                    </button>

                    <h4>视频控制</h4>
                    <button class="dropdown-btn btn-success" onclick="admin.showVideoPreview('${deviceId}')">
                        📺 视频流预览
                    </button>
                    <button class="dropdown-btn btn-primary" onclick="admin.showVideoControls('${deviceId}')">
                        🎥 视频参数设置
                    </button>
                    <button class="dropdown-btn btn-info" onclick="admin.takeScreenshotFromStream('${deviceId}')">
                        📸 视频流截屏
                    </button>
                </div>

                <div class="dropdown-section">
                    <h4>游戏控制</h4>
                    <button class="dropdown-btn btn-success" onclick="admin.showGameControls('${deviceId}')">
                        🎮 游戏设置
                    </button>

                    <h4>日志管理</h4>
                    <button class="dropdown-btn btn-info" onclick="admin.sendQuickCommand('${deviceId}', 'toggle_log_display', {enabled: true})">
                        📝 开启日志显示
                    </button>
                    <button class="dropdown-btn btn-warning" onclick="admin.sendQuickCommand('${deviceId}', 'toggle_log_display', {enabled: false})">
                        🚫 关闭日志显示
                    </button>
                    <button class="dropdown-btn btn-primary" onclick="admin.sendQuickCommand('${deviceId}', 'download_logs', {method: 'ftp'})">
                        📥 下载日志(FTP)
                    </button>
                </div>

                <div class="dropdown-section">
                    <h4>网络配置</h4>
                    <button class="dropdown-btn btn-primary" onclick="admin.showStunTurnModal()">
                        🌐 STUN/TURN配置
                    </button>
                    <button class="dropdown-btn btn-info" onclick="admin.sendConfigToDevice('${deviceId}')">
                        📡 发送网络配置
                    </button>

                    <h4>系统控制</h4>
                    <button class="dropdown-btn btn-danger" onclick="admin.confirmReboot('${deviceId}')">
                        🔄 重启设备
                    </button>
                    <button class="dropdown-btn btn-warning" onclick="admin.showUpgradeDialog('${deviceId}')">
                        📦 升级应用
                    </button>
                </div>
            </div>
        `;
    }

    // 切换下拉菜单显示
    toggleDropdown(deviceId) {
        // 防抖：如果有正在进行的操作，取消它
        if (this.dropdownTimeout) {
            clearTimeout(this.dropdownTimeout);
        }

        // 延迟执行，防止快速连续点击
        this.dropdownTimeout = setTimeout(() => {
            this.executeToggleDropdown(deviceId);
        }, 50);
    }

    // 实际执行下拉菜单切换
    executeToggleDropdown(deviceId) {
        const timestamp = new Date().toLocaleTimeString();
        const stack = new Error().stack;
        console.log(`🔄 [${timestamp}] 执行切换:`, deviceId);
        console.log('📍 调用堆栈:', stack);

        // 关闭所有其他下拉菜单和按钮状态
        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            if (menu.id !== `dropdown-${deviceId}`) {
                const wasShowing = menu.classList.contains('show');
                if (wasShowing) {
                    console.log(`❌ [${timestamp}] 关闭其他菜单:`, menu.id, '原因: 切换到新菜单');
                    this.logDropdownStateChange(menu.id, 'show', 'hidden', '切换到新菜单', stack);
                }
                menu.classList.remove('show');
                // 重置位置样式
                menu.style.removeProperty('left');
                menu.style.removeProperty('top');
                menu.style.removeProperty('visibility');
                menu.style.removeProperty('opacity');
                menu.style.removeProperty('display');
                // 移除位置锁定标记
                menu.removeAttribute('data-positioned');
            }
        });
        document.querySelectorAll('.dropdown-toggle').forEach(btn => {
            if (btn.onclick.toString().indexOf(deviceId) === -1) {
                btn.classList.remove('active');
            }
        });

        // 切换当前下拉菜单和按钮状态
        const dropdown = document.getElementById(`dropdown-${deviceId}`);
        const button = document.querySelector(`[onclick="admin.toggleDropdown('${deviceId}')"]`);

        if (!dropdown || !button) {
            console.error('❌ 找不到元素:', { dropdown, button });
            return;
        }

        const isShowing = dropdown.classList.contains('show');
        console.log(`📊 [${timestamp}] 当前状态:`, isShowing ? '显示' : '隐藏');

        if (isShowing) {
            // 隐藏下拉菜单
            console.log(`❌ [${timestamp}] 隐藏菜单:`, deviceId, '原因: 用户点击切换');
            this.logDropdownStateChange(deviceId, 'show', 'hidden', '用户点击切换', stack);

            dropdown.classList.remove('show');
            button.classList.remove('active');
            dropdown.removeAttribute('data-positioned');

            // 完全清理样式
            dropdown.style.removeProperty('left');
            dropdown.style.removeProperty('top');
            dropdown.style.removeProperty('visibility');
            dropdown.style.removeProperty('opacity');
            dropdown.style.removeProperty('display');
        } else {
            // 显示下拉菜单 - 使用简化版的成功逻辑
            console.log(`✅ [${timestamp}] 显示菜单:`, deviceId, '原因: 用户点击切换');
            this.logDropdownStateChange(deviceId, 'hidden', 'show', '用户点击切换', stack);

            // 清除之前的定位标记
            dropdown.removeAttribute('data-positioned');

            // 先计算位置
            this.adjustDropdownPosition(dropdown, button);

            // 再显示菜单
            dropdown.classList.add('show');
            button.classList.add('active');

            // 设置监听器检测意外关闭
            this.setupDropdownWatcher(deviceId);
        }
    }

    // 调整下拉菜单位置（使用简化版的成功逻辑）
    adjustDropdownPosition(dropdown, button) {
        // 如果已经定位过，不要重复计算
        if (dropdown.hasAttribute('data-positioned')) {
            console.log('已定位，跳过重复计算');
            return;
        }

        console.log('开始位置计算 - 使用简化版逻辑');

        // 使用简化版的成功方法：先设置为可见但透明
        dropdown.style.visibility = 'hidden';
        dropdown.style.opacity = '0';
        dropdown.style.display = 'block';

        // 强制重新渲染
        dropdown.offsetHeight;

        // 获取真实尺寸
        const buttonRect = button.getBoundingClientRect();
        const dropdownRect = dropdown.getBoundingClientRect();
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;

        // 使用真实测量的尺寸
        const menuWidth = dropdownRect.width;
        const menuHeight = dropdownRect.height;

        console.log('尺寸信息:', {
            button: { left: buttonRect.left, top: buttonRect.top, bottom: buttonRect.bottom, right: buttonRect.right },
            dropdown: { width: menuWidth, height: menuHeight },
            window: { width: windowWidth, height: windowHeight }
        });

        // 默认位置：按钮下方左对齐
        let left = buttonRect.left;
        let top = buttonRect.bottom + 10;

        console.log('初始位置:', { left, top });

        // 检查右边界
        if (left + menuWidth > windowWidth - 20) {
            left = buttonRect.right - menuWidth;
            console.log('调整右边界，新left:', left);
        }

        // 检查左边界
        if (left < 20) {
            left = 20;
            console.log('调整左边界，新left:', left);
        }

        // 检查下边界
        if (top + menuHeight > windowHeight - 20) {
            top = buttonRect.top - menuHeight - 10;
            console.log('调整下边界，新top:', top);
        }

        // 检查上边界
        if (top < 20) {
            top = 20;
            console.log('调整上边界，新top:', top);
        }

        console.log('最终位置:', { left, top });

        // 设置位置（不使用!important，避免CSS冲突）
        dropdown.style.left = `${left}px`;
        dropdown.style.top = `${top}px`;
        dropdown.style.visibility = 'visible';
        dropdown.style.opacity = '1';
        dropdown.setAttribute('data-positioned', 'true');

        console.log('位置设置完成，已锁定');
    }

    // 显示视频控制对话框
    showVideoControls(deviceId) {
        this.currentDeviceId = deviceId;

        const content = `
            <div class="form-row">
                <div class="form-col">
                    <label>分辨率</label>
                    <select id="videoResolution">
                        <option value="576p">576p (1024x576)</option>
                        <option value="720p" selected>720p (1280x720)</option>
                        <option value="1080p">1080p (1920x1080)</option>
                        <option value="custom">自定义</option>
                    </select>
                </div>
                <div class="form-col" id="customResolutionDiv" style="display: none;">
                    <label>自定义分辨率</label>
                    <input type="text" id="customResolution" placeholder="1920x1080">
                </div>
            </div>
            <div class="form-row">
                <div class="form-col">
                    <label>码率 (kbps)</label>
                    <input type="number" id="videoBitrate" value="3000" min="500" max="10000">
                </div>
                <div class="form-col">
                    <label>帧率 (fps)</label>
                    <select id="videoFramerate">
                        <option value="30">30 fps</option>
                        <option value="60" selected>60 fps</option>
                    </select>
                </div>
            </div>
        `;

        this.currentCommand = () => {
            const resolutionSelect = document.getElementById('videoResolution');
            const customResolution = document.getElementById('customResolution');
            const bitrate = document.getElementById('videoBitrate');
            const framerate = document.getElementById('videoFramerate');

            let resolution = resolutionSelect.value;
            if (resolution === 'custom') {
                resolution = customResolution.value.trim();
                if (!resolution) {
                    this.showToast('error', '错误', '请输入自定义分辨率');
                    return false;
                }
            }

            this.sendQuickCommand(deviceId, 'change_resolution', {
                resolution: resolution,
                bitrate: parseInt(bitrate.value),
                framerate: parseInt(framerate.value)
            });
            return true;
        };

        this.showModal('🎥 视频参数设置', content, this.currentCommand);

        // 添加分辨率选择监听
        setTimeout(() => {
            document.getElementById('videoResolution').addEventListener('change', (e) => {
                const customDiv = document.getElementById('customResolutionDiv');
                if (e.target.value === 'custom') {
                    customDiv.style.display = 'block';
                } else {
                    customDiv.style.display = 'none';
                }
            });
        }, 100);

        this.closeAllDropdowns();
    }

    // 显示游戏控制对话框
    showGameControls(deviceId) {
        this.currentDeviceId = deviceId;

        const content = `
            <div class="checkbox-wrapper">
                <input type="checkbox" id="gameEnabled" checked>
                <label for="gameEnabled">启用自动启动游戏</label>
            </div>
            <div class="form-row">
                <div class="form-col">
                    <label>游戏包名 (可选)</label>
                    <input type="text" id="gamePackage" placeholder="com.example.game 或留空使用自动检测">
                </div>
            </div>
            <div class="form-row">
                <div class="form-col">
                    <label>常用游戏包名</label>
                    <select id="commonGames" onchange="document.getElementById('gamePackage').value = this.value">
                        <option value="">选择常用游戏...</option>
                        <option value="com.tencent.tmgp.sgame">王者荣耀</option>
                        <option value="com.tencent.tmgp.pubgmhd">和平精英</option>
                        <option value="com.netease.dwrg">第五人格</option>
                        <option value="com.miHoYo.GenshinImpact">原神</option>
                        <option value="com.supercell.clashofclans">部落冲突</option>
                    </select>
                </div>
            </div>
            <p style="color: #6c757d; font-size: 13px; margin-top: 10px;">
                💡 提示：留空包名将自动检测包含 bird、ocean、demo 关键字的游戏
            </p>
        `;

        this.currentCommand = () => {
            const enabled = document.getElementById('gameEnabled').checked;
            const packageName = document.getElementById('gamePackage').value.trim();

            this.sendQuickCommand(deviceId, 'set_auto_start_game', {
                enabled: enabled,
                package_name: packageName
            });
            return true;
        };

        this.showModal('🎮 游戏启动设置', content, this.currentCommand);
        this.closeAllDropdowns();
    }

    // 确认重启设备
    confirmReboot(deviceId) {
        if (confirm(`确定要重启设备 ${deviceId} 吗？此操作不可撤销！`)) {
            this.sendQuickCommand(deviceId, 'reboot_device');
        }
        this.closeAllDropdowns();
    }

    // 显示升级对话框
    showUpgradeDialog(deviceId) {
        this.currentDeviceId = deviceId;

        const content = `
            <div class="form-row">
                <div class="form-col">
                    <label>常用升级包</label>
                    <select id="commonPackages" onchange="admin.fillUpgradePackage()">
                        <option value="">选择常用包...</option>
                        <option value="http://39.96.165.173:8888/down/XdFBq5q171Qu.apk|2.1.0|false">正式版 v2.1.0</option>
                        <option value="http://39.96.165.173:8888/down/test_v2.0.5.apk|2.0.5|false">测试版 v2.0.5</option>
                        <option value="http://39.96.165.173:8888/down/beta_v2.2.0.apk|2.2.0|true">Beta版 v2.2.0 (强制)</option>
                        <option value="http://39.96.165.173:8888/down/stable_v2.0.8.apk|2.0.8|false">稳定版 v2.0.8</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-col">
                    <label>APK下载地址</label>
                    <input type="url" id="apkUrl" placeholder="https://example.com/app.apk" value="http://39.96.165.173:8888/down/XdFBq5q171Qu.apk" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-col">
                    <label>版本号</label>
                    <input type="text" id="appVersion" placeholder="1.0.0" value="2.1.0" required>
                </div>
                <div class="form-col">
                    <label>升级类型</label>
                    <select id="upgradeType">
                        <option value="false">普通升级</option>
                        <option value="true">强制升级</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-col">
                    <label>升级说明</label>
                    <input type="text" id="upgradeNote" placeholder="升级说明 (可选)">
                </div>
            </div>
            <p style="color: #dc3545; font-size: 13px; margin-top: 10px;">
                ⚠️ 警告：升级过程中设备将重启应用，请确保APK地址可访问
            </p>
        `;

        this.currentCommand = () => {
            const apkUrl = document.getElementById('apkUrl').value.trim();
            const version = document.getElementById('appVersion').value.trim();
            const force = document.getElementById('upgradeType').value === 'true';
            const note = document.getElementById('upgradeNote').value.trim();

            if (!apkUrl || !version) {
                this.showToast('error', '错误', '请填写完整的升级信息');
                return false;
            }

            const upgradeParams = {
                apk_url: apkUrl,
                version: version,
                force: force
            };

            if (note) {
                upgradeParams.note = note;
            }

            this.sendQuickCommand(deviceId, 'upgrade', upgradeParams);
            return true;
        };

        this.showModal('📦 应用升级', content, this.currentCommand);
        this.closeAllDropdowns();
    }

    // 填充升级包信息
    fillUpgradePackage() {
        const select = document.getElementById('commonPackages');
        const selectedValue = select.value;

        if (!selectedValue) return;

        const [apkUrl, version, force] = selectedValue.split('|');

        document.getElementById('apkUrl').value = apkUrl;
        document.getElementById('appVersion').value = version;
        document.getElementById('upgradeType').value = force;

        console.log('已填充升级包信息:', { apkUrl, version, force });
    }

    // 从视频流截屏
    takeScreenshotFromStream(deviceId) {
        this.currentDeviceId = deviceId;

        const content = `
            <p>📹 此功能将从当前视频流中抽取一帧画面作为截屏</p>
            <div class="form-row">
                <div class="form-col">
                    <label>截屏说明</label>
                    <input type="text" id="screenshotNote" placeholder="截屏备注 (可选)">
                </div>
            </div>
            <p style="color: #17a2b8; font-size: 13px; margin-top: 10px;">
                💡 提示：截屏将从正在录制的视频流中提取当前帧，而不是调用系统截屏API
            </p>
        `;

        this.currentCommand = () => {
            const note = document.getElementById('screenshotNote').value.trim();
            const requestId = `admin_${Date.now()}_${deviceId}`;

            this.sendQuickCommand(deviceId, 'take_screenshot', {
                request_id: requestId,
                note: note,
                source: 'video_stream' // 标明是从视频流截取
            });

            this.showToast('info', '截屏请求', '正在从视频流中抽取当前帧...');
            return true;
        };

        this.showModal('📸 视频流截屏', content, this.currentCommand);
        this.closeAllDropdowns();
    }

    // 下拉菜单状态变化日志记录
    logDropdownStateChange(deviceId, fromState, toState, reason, stack) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = {
            timestamp,
            deviceId,
            fromState,
            toState,
            reason,
            stack: stack ? stack.split('\n').slice(0, 5).join('\n') : 'N/A'
        };

        console.log(`🔍 [${timestamp}] 下拉菜单状态变化:`, logEntry);

        // 存储到全局变量以便调试
        if (!window.dropdownStateLog) {
            window.dropdownStateLog = [];
        }
        window.dropdownStateLog.push(logEntry);

        // 只保留最近50条记录
        if (window.dropdownStateLog.length > 50) {
            window.dropdownStateLog = window.dropdownStateLog.slice(-50);
        }
    }

    // 设置下拉菜单监听器检测意外关闭
    setupDropdownWatcher(deviceId) {
        const dropdown = document.getElementById(`dropdown-${deviceId}`);
        if (!dropdown) return;

        console.log(`👁️ 设置监听器:`, deviceId);

        // 移除之前的监听器
        if (dropdown._stateWatcher) {
            dropdown._stateWatcher.disconnect();
        }
        if (dropdown._styleWatcher) {
            dropdown._styleWatcher.disconnect();
        }

        // 创建MutationObserver监听class变化
        const classObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const hasShow = dropdown.classList.contains('show');
                    const hadShow = mutation.oldValue && mutation.oldValue.includes('show');

                    if (hadShow && !hasShow) {
                        // 菜单被意外关闭
                        const timestamp = new Date().toLocaleTimeString();
                        const stack = new Error().stack;
                        console.log(`⚠️ [${timestamp}] 检测到菜单意外关闭:`, deviceId);
                        console.log('📍 关闭时的调用堆栈:', stack);
                        this.logDropdownStateChange(deviceId, 'show', 'hidden', '意外关闭(MutationObserver检测)', stack);
                    }
                }
            });
        });

        // 创建MutationObserver监听样式变化（检测闪烁）
        const styleObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' &&
                    (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
                    const timestamp = new Date().toLocaleTimeString();
                    const currentStyle = {
                        display: dropdown.style.display,
                        visibility: dropdown.style.visibility,
                        opacity: dropdown.style.opacity,
                        left: dropdown.style.left,
                        top: dropdown.style.top
                    };

                    console.log(`🎨 [${timestamp}] 样式变化检测:`, deviceId, {
                        属性: mutation.attributeName,
                        旧值: mutation.oldValue,
                        新样式: currentStyle,
                        计算样式: {
                            display: getComputedStyle(dropdown).display,
                            visibility: getComputedStyle(dropdown).visibility,
                            opacity: getComputedStyle(dropdown).opacity
                        }
                    });
                }
            });
        });

        classObserver.observe(dropdown, {
            attributes: true,
            attributeOldValue: true,
            attributeFilter: ['class']
        });

        styleObserver.observe(dropdown, {
            attributes: true,
            attributeOldValue: true,
            attributeFilter: ['style', 'class']
        });

        dropdown._stateWatcher = classObserver;
        dropdown._styleWatcher = styleObserver;

        // 5秒后检查菜单是否还在显示
        setTimeout(() => {
            if (dropdown.classList.contains('show')) {
                console.log(`✅ 菜单状态正常:`, deviceId, '5秒后仍在显示');
            } else {
                console.log(`⚠️ 菜单已关闭:`, deviceId, '5秒后检查发现已关闭');
            }
        }, 5000);
    }

    // 关闭所有下拉菜单
    closeAllDropdowns() {
        const timestamp = new Date().toLocaleTimeString();
        const stack = new Error().stack;
        console.log(`🔄 [${timestamp}] 关闭所有下拉菜单`);
        console.log('📍 调用堆栈:', stack);

        document.querySelectorAll('.dropdown-menu').forEach(menu => {
            const wasShowing = menu.classList.contains('show');
            if (wasShowing) {
                const deviceId = menu.id.replace('dropdown-', '');
                console.log(`❌ [${timestamp}] 关闭菜单:`, deviceId, '原因: closeAllDropdowns调用');
                this.logDropdownStateChange(deviceId, 'show', 'hidden', 'closeAllDropdowns调用', stack);
            }

            menu.classList.remove('show');
            // 清除所有定位样式
            menu.style.removeProperty('left');
            menu.style.removeProperty('top');
            menu.style.removeProperty('visibility');
            menu.style.removeProperty('opacity');
            menu.style.removeProperty('display');
            // 移除位置锁定标记
            menu.removeAttribute('data-positioned');

            // 清理监听器
            if (menu._stateWatcher) {
                menu._stateWatcher.disconnect();
                menu._stateWatcher = null;
            }
        });
        document.querySelectorAll('.dropdown-toggle').forEach(btn => {
            btn.classList.remove('active');
        });
    }

    // WebSocket连接由AdminWebSocket类统一管理

    // WebSocket消息处理已移至AdminWebSocket类

    // 显示消息提示
    showToast(type, title, message, duration = 5000) {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;

        toast.innerHTML = `
            <div class="toast-header">
                <div class="toast-title">${title}</div>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">&times;</button>
            </div>
            <div class="toast-body">${message}</div>
        `;

        container.appendChild(toast);

        // 显示动画
        setTimeout(() => toast.classList.add('show'), 100);

        // 自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, duration);
    }

    // 显示模态对话框
    showModal(title, content, confirmCallback) {
        document.getElementById('modalTitle').textContent = title;
        document.getElementById('modalBody').innerHTML = content;

        const modal = document.getElementById('commandModal');
        const confirmBtn = document.getElementById('modalConfirm');

        // 设置确认回调
        confirmBtn.onclick = () => {
            if (confirmCallback && confirmCallback()) {
                this.closeModal();
            }
        };

        modal.classList.add('show');
    }

    // 关闭模态对话框
    closeModal() {
        const modal = document.getElementById('commandModal');
        modal.classList.remove('show');
        this.currentCommand = null;
        this.currentDeviceId = null;
    }

    // 确认命令执行
    confirmCommand() {
        if (this.currentCommand && this.currentDeviceId) {
            const result = this.currentCommand();
            return result !== false;
        }
        return true;
    }
}

// 全局函数，供HTML调用
let admin;

window.addEventListener('DOMContentLoaded', () => {
    admin = new SignalingServerAdmin();

    // 在admin对象创建后添加STUN/TURN配置相关方法
    admin.showStunTurnModal = function() {
        const modal = document.getElementById('stunTurnModal');
        const stunServers = document.getElementById('modalStunServers');
        const turnServers = document.getElementById('modalTurnServers');

        // 加载当前配置
        stunServers.value = document.getElementById('stunServers').value;
        turnServers.value = document.getElementById('turnServers').value;

        modal.classList.add('show');
    };

    admin.closeStunTurnModal = function() {
        const modal = document.getElementById('stunTurnModal');
        modal.classList.remove('show');
    };

    admin.applyStunPreset = function() {
        const preset = document.getElementById('stunPresets').value;
        const stunServers = document.getElementById('modalStunServers');

        switch(preset) {
            case 'google':
                stunServers.value = 'stun:stun.l.google.com:19302\nstun:stun1.l.google.com:19302\nstun:stun2.l.google.com:19302';
                break;
            case 'mozilla':
                stunServers.value = 'stun:stun.services.mozilla.com';
                break;
            case 'custom':
                stunServers.value = '';
                break;
        }
    };

    admin.applyTurnPreset = function() {
        const preset = document.getElementById('turnPresets').value;
        const turnServers = document.getElementById('modalTurnServers');

        switch(preset) {
            case 'numb':
                turnServers.value = JSON.stringify([{
                    "urls": "turn:numb.viagenie.ca",
                    "username": "<EMAIL>",
                    "credential": "muazkh"
                }], null, 2);
                break;
            case 'xirsys':
                turnServers.value = JSON.stringify([{
                    "urls": "turn:xirsys.com",
                    "username": "your_username",
                    "credential": "your_credential"
                }], null, 2);
                break;
            case 'custom':
                turnServers.value = '';
                break;
        }
    };

    admin.saveStunTurnConfig = function() {
        const stunServers = document.getElementById('modalStunServers').value;
        const turnServers = document.getElementById('modalTurnServers').value;

        // 更新主配置区域
        document.getElementById('stunServers').value = stunServers;
        document.getElementById('turnServers').value = turnServers;

        // 保存配置
        admin.updateConfig();
        admin.closeStunTurnModal();
    };

    admin.saveAndBroadcastStunTurn = function() {
        const stunServers = document.getElementById('modalStunServers').value;
        const turnServers = document.getElementById('modalTurnServers').value;

        // 更新主配置区域
        document.getElementById('stunServers').value = stunServers;
        document.getElementById('turnServers').value = turnServers;

        // 保存并广播配置
        admin.updateConfigAndBroadcast();
        admin.closeStunTurnModal();
    };

    admin.sendConfigToDevice = function(deviceId) {
        admin.sendQuickCommand(deviceId, 'update_config', {
            stun_servers: document.getElementById('stunServers').value.split('\n').filter(s => s.trim()),
            turn_servers: JSON.parse(document.getElementById('turnServers').value || '[]')
        });
    };
});

// 导出函数供HTML按钮调用
function loadCurrentConfig() {
    admin.loadCurrentConfig();
}

function updateConfig() {
    admin.updateConfig(false);
}

function updateConfigAndBroadcast() {
    admin.updateConfig(true);
}

function broadcastConfig() {
    admin.broadcastConfig();
}

function refreshSenders() {
    admin.refreshSenders();
}

function refreshReceivers() {
    admin.refreshReceivers();
}

function sendCommand() {
    admin.sendCommand();
}

function sendUpgrade() {
    admin.sendUpgrade();
}

function sendConfigToSelected() {
    admin.sendConfigToSelected();
}

function batchCommand() {
    admin.batchCommand();
}



