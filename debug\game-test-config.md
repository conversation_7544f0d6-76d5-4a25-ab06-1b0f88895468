# 游戏测试功能配置说明

## 概述

游戏测试功能通过WebSocket连接到实际的游戏服务器，执行完整的游戏流程测试，包括：
- 客户端注册
- 游戏状态查询
- 开始游戏
- 投币操作
- 游戏操作命令
- 结束游戏
- 心跳维持

## WebSocket连接地址

游戏测试使用以下格式的WebSocket地址：
```
ws://[房间服务器域名]/websocket
```

## 房间服务器域名配置

系统会按以下优先级获取房间服务器域名：

### 1. 设备配置的房间服务器域名
如果设备数据中包含 `room_server_domain` 字段：
```json
{
  "id": "gamev-3e02d66a",
  "room_server_domain": "http://*************:8080"
}
```

### 2. 从设备域名构建
如果设备有 `domain` 字段：
```json
{
  "id": "gamev-3e02d66a", 
  "domain": "*************"
}
```
系统会构建为：`http://*************:8080`

### 3. 从设备IP构建
如果设备有 `ip` 字段：
```json
{
  "id": "gamev-3e02d66a",
  "ip": "*************"
}
```
系统会构建为：`http://*************:8080`

### 4. 从设备ID提取IP
如果设备ID格式为 `IP-其他信息`：
```json
{
  "id": "*************-gamev-3e02d66a"
}
```
系统会提取IP并构建为：`http://*************:8080`

## 测试流程

### 1. 连接阶段
- 建立WebSocket连接到 `ws://[域名]/websocket/`
- 等待连接成功确认

### 2. 注册阶段
发送注册消息：
```json
{
  "type": "register",
  "client_type": "uniapp",
  "gameSn": "d47905e3"
}
```

**注意**：`gameSn` 使用设备的 `cpu_unique_id`，不是随机生成的序列号。系统会按以下优先级获取：
1. `device.cpu_unique_id` - 设备CPU唯一标识符
2. `device.gameSn` - 设备配置的游戏序列号
3. `deviceId` - 设备ID作为后备选项

期待响应：
```json
{
  "code": 200,
  "type": "register_back",
  "status": "success"
}
```

### 3. 游戏状态通知
服务器会主动发送游戏状态信息（无需客户端请求）：
```json
{
  "code": 200,
  "type": "queryGameState_back",
  "status": "success",
  "data": {
    "cachedCoin": 0,
    "gameLogId": null,
    "gameStatus": "idle",
    "coinUsed": 0
  }
}
```

### 4. 开始游戏
发送开始游戏消息：
```json
{
  "type": "startGame",
  "number": 1,
  "userId": 9
}
```

期待响应：
```json
{
  "code": 200,
  "type": "startGame_back",
  "status": "success"
}
```

### 5. 投币操作
发送投币消息：
```json
{
  "type": "putInCoins",
  "number": 1,
  "coin": 30,
  "requestId": "putInCoins_1756868820795_ewkkjqh5n"
}
```

期待响应：
```json
{
  "code": 200,
  "type": "putInCoinsResult",
  "status": "success",
  "message": "投币成功",
  "data": {
    "type": "putInCoinsResult",
    "code": 200,
    "order_id": "17568688203572",
    "requestId": "putInCoins_1756868820795_ewkkjqh5n",
    "coin": 30,
    "message": "投币成功"
  }
}
```

### 6. 游戏操作命令
发送一系列游戏操作命令：
```json
{
  "type": "command",
  "number": 1,
  "status": 13  // 不同的状态值代表不同操作
}
```

命令序列：
- status: 13 (发送2次，间隔500ms)
- status: 12 (发送4次，间隔200ms)  
- status: 15 (发送10次，间隔150ms)
- status: 17 (发送1次)

每个命令期待响应：
```json
{
  "code": 200,
  "type": "command_back",
  "status": "success"
}
```

### 7. 结束游戏
发送结束游戏消息：
```json
{
  "type": "endGame",
  "gameLogId": 1754
}
```

期待响应：
```json
{
  "code": 200,
  "type": "endGame_back",
  "status": "success",
  "message": "游戏已成功结束！赢得金币：24",
  "data": {
    "won_coins": 24,
    "used_coins": 300
  }
}
```

### 8. 心跳维持
连接建立后，每秒发送心跳：
```json
{
  "type": "heartbeat"
}
```

期待响应：
```json
{
  "code": 200,
  "type": "heartbeat_back",
  "status": "success"
}
```

## 错误处理

### 连接错误
- WebSocket连接超时（10秒）
- 连接被拒绝
- 网络不可达

### 响应错误
- 响应超时（各步骤有不同超时时间）
- 响应格式错误
- 服务器返回错误状态

### 特殊错误处理

#### 411错误 - 游戏正在进行中
当收到结束游戏的411错误响应时：
```json
{
  "code": 411,
  "type": "endGame_back",
  "status": "fail",
  "message": "游戏正在进行中，请稍后再尝试退出",
  "data": {
    "action": "cancel_settlement",
    "continue_game": true
  }
}
```

**自动重试机制**：
- 等待3秒后自动重试结束游戏
- 最多重试3次
- 每次重试都会在设备卡片上显示进度
- 重试信息会记录在消息分析中

### 状态监控
测试过程中会实时更新设备卡片上的状态：
- 🔵 测试中：显示当前步骤
- 🟢 成功：测试完成
- 🔴 错误：显示错误信息
- 🟡 警告：部分步骤失败

### 消息分析
设备卡片会显示详细的消息分析信息：
- 📊 重试信息：显示结束游戏重试次数和原因
- 📊 错误统计：统计测试过程中的错误消息
- 📊 消息计数：显示发送和接收的消息数量
- 📊 消息类型：统计各种消息类型的数量

**消息记录包含**：
- 所有发送和接收的消息
- 消息时间戳
- 重试标记
- 错误代码和原因

## 使用方法

### 批量测试
1. 点击域名下的"🎯 批量测试"按钮
2. 系统会并行启动该域名下所有在线设备的测试

### 单设备测试  
1. 点击设备卡片的齿轮按钮
2. 选择"🎯 游戏测试"
3. 查看设备卡片上的实时状态更新

### 查看测试结果
- 设备卡片显示测试状态
- 日志区域显示详细测试信息
- 浏览器控制台显示完整的消息交互日志

## 故障排除

### 常见问题
1. **连接超时**：检查房间服务器是否运行，端口是否正确
2. **响应超时**：检查游戏服务器是否正常响应
3. **域名配置错误**：确保设备配置了正确的房间服务器域名
4. **网络问题**：检查网络连接和防火墙设置

### 调试建议
1. 打开浏览器开发者工具查看控制台日志
2. 检查WebSocket连接状态
3. 验证消息格式是否正确
4. 确认服务器端口和路径配置
