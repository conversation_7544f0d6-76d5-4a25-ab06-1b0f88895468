(com.example.webrtcsender.WebRTCSenderApp2com.example.webrtcsender.WebRTCSenderApp.Companion0com.example.webrtcsender.audio.AudioSourceTester:<EMAIL>@com.example.webrtcsender.audio.CaptureCardAudioManager.Companion2com.example.webrtcsender.command.CommandDispatcher<com.example.webrtcsender.command.CommandDispatcher.Companion/com.example.webrtcsender.command.CommandHandler9com.example.webrtcsender.command.CommandHandler.Companion5com.example.webrtcsender.command.ConfigCommandHandler?<EMAIL>?<EMAIL>>com.example.webrtcsender.command.VideoCommandHandler.Companion.com.example.webrtcsender.receiver.BootReceiver8com.example.webrtcsender.receiver.BootReceiver.Companion0com.example.webrtcsender.receiver.RebootReceiver:<EMAIL>><EMAIL><com.example.webrtcsender.signaling.SignalingClient.Companion:com.example.webrtcsender.signaling.SignalingClientListener*com.example.webrtcsender.ui.AppListAdapter8com.example.webrtcsender.ui.AppListAdapter.AppViewHolder/com.example.webrtcsender.ui.AppSelectorActivity9com.example.webrtcsender.ui.AppSelectorActivity.Companion0com.example.webrtcsender.ui.CameraConfigActivity:com.example.webrtcsender.ui.CameraConfigActivity.Companion(com.example.webrtcsender.ui.MainActivity2com.example.webrtcsender.ui.MainActivity.Companion,com.example.webrtcsender.ui.SettingsActivity6com.example.webrtcsender.ui.SettingsActivity.Companion0com.example.webrtcsender.utils.AutoRebootManager:com.example.webrtcsender.utils.AutoRebootManager.Companion(com.example.webrtcsender.utils.Constants2com.example.webrtcsender.utils.DeviceInfoCollector<com.example.webrtcsender.utils.DeviceInfoCollector.Companion1com.example.webrtcsender.utils.DeviceInfoReporter;com.example.webrtcsender.utils.DeviceInfoReporter.Companion/com.example.webrtcsender.utils.DeviceLogManager9com.example.webrtcsender.utils.DeviceLogManager.Companion*com.example.webrtcsender.utils.DeviceUtils8com.example.webrtcsender.utils.FirstInstallConfigManagerAcom.example.webrtcsender.utils.FirstInstallConfigManager.GameInfo+com.example.webrtcsender.utils.GameLauncher)com.example.webrtcsender.utils.LogManager%com.example.webrtcsender.utils.Logger0com.example.webrtcsender.utils.ScreenshotManager,<EMAIL><com.example.webrtcsender.webrtc.WebRTCClient.ConnectionState9com.example.webrtcsender.webrtc.WebRTCClient.NetworkStatsAcom.example.webrtcsender.webrtc.WebRTCClient.WebRTCClientListener-com.example.webrtcsender.webrtc.WebRTCManagerCcom.example.webrtcsender.webrtc.WebRTCManager.WebRTCManagerListener8com.example.webrtcsender.webrtc.WebRTCManager.CameraInfo$com.example.webrtcsender.BuildConfig/com.example.webrtcsender.utils.ZtlInstallHelper+com.example.webrtcsender.utils.UploadResult                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             