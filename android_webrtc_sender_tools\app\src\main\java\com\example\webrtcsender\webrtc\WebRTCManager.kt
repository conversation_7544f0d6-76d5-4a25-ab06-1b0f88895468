package com.example.webrtcsender.webrtc

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.media.projection.MediaProjection
import android.os.Handler
import android.os.Looper
import com.example.webrtcsender.WebRTCSenderApp
import com.example.webrtcsender.service.WebRTCSenderService
import com.example.webrtcsender.signaling.SignalingClient
import com.example.webrtcsender.signaling.SignalingClientListener
import com.example.webrtcsender.utils.Constants
import com.example.webrtcsender.utils.DeviceUtils
import com.example.webrtcsender.utils.DeviceInfoReporter
import com.example.webrtcsender.utils.Logger
import com.example.webrtcsender.utils.AutoRebootManager
import com.example.webrtcsender.utils.DeviceLogManager
import com.google.gson.Gson
import org.webrtc.IceCandidate
import org.webrtc.PeerConnection
import org.webrtc.PeerConnection.*
import org.webrtc.SessionDescription
import org.webrtc.VideoTrack
import org.webrtc.MediaStreamTrack
import org.webrtc.Camera2Enumerator
import org.webrtc.Camera1Enumerator
import java.util.*
import kotlinx.coroutines.*

/**
 * WebRTC管理器，负责协调WebRTC客户端和信令客户端
 */
object WebRTCManager : WebRTCClient.WebRTCClientListener, SignalingClientListener {
    private const val TAG = "WebRTCManager"

    // Gson实例
    val gson = Gson()

    // 主线程Handler
    private val mainHandler = Handler(Looper.getMainLooper())

    // WebRTC客户端
    var webRTCClient: WebRTCClient? = null

    // 信令客户端
    var signalingClient: SignalingClient? = null

    // 设备信息上报器
    private var deviceInfoReporter: DeviceInfoReporter? = null

    // 监听器
    private var listener: WebRTCManagerListener? = null

    // 状态
    private var isInitialized = false
    private var isConnected = false
    private var hasBeenConnectedBefore = false
    private var isPaused = false

    // 视频源状态
    private var currentVideoSource = Constants.DEFAULT_VIDEO_SOURCE
    private var mediaProjection: MediaProjection? = null
    private var mediaProjectionData: Intent? = null

    // 防重复启动
    private var isVideoSourceStarting = false
    private var isVideoSourceActive = false
    private var lastVideoSourceStartTime = 0L
    private val VIDEO_SOURCE_START_COOLDOWN = 3000L // 3秒冷却时间

    // 自动重启相关
    private var videoSourceFailureCount = 0
    private var lastFailureTime = 0L
    private val maxFailureCount = 3 // 连续失败3次触发重启
    private val failureResetInterval = 300000L // 5分钟内的失败才计数

    // 广播频率控制
    private var lastBroadcastTime = 0L
    private val BROADCAST_COOLDOWN = 5000L // 5秒广播冷却时间

    // 连接超时管理
    private val viewers = mutableSetOf<String>()
    private val connectionTimeouts = mutableMapOf<String, Long>()
    private val connectionTimeoutHandler = Handler(Looper.getMainLooper())
    private val CONNECTION_TIMEOUT = 30000L // 30秒连接超时

    /**
     * 初始化WebRTC管理器
     */
    fun initialize(context: Context, listener: WebRTCManagerListener) {
        if (isInitialized) {
            Logger.w(TAG, "WebRTC管理器已经初始化")
            return
        }

        Logger.i(TAG, "🚀 开始初始化WebRTC管理器")

        try {
            this.listener = listener

            // 创建WebRTC客户端
            Logger.i(TAG, "🔧 创建WebRTC客户端...")
            webRTCClient = WebRTCClient(context, this)

            Logger.i(TAG, "🔧 初始化WebRTC客户端...")
            webRTCClient?.initialize()

            // 创建信令客户端
            Logger.i(TAG, "🔧 创建信令客户端...")
            signalingClient = SignalingClient(context, this, this)

            // 创建设备信息上报器
            Logger.i(TAG, "🔧 创建设备信息上报器...")
            deviceInfoReporter = DeviceInfoReporter(context, signalingClient!!)

            // 初始化设备日志管理器
            Logger.i(TAG, "🔧 初始化设备日志管理器...")
            DeviceLogManager.getInstance().initialize(signalingClient!!)

            // 启动连接超时检查
            startConnectionTimeoutCheck()

            isInitialized = true
            Logger.i(TAG, "✅ WebRTC管理器初始化完成")
        } catch (e: Exception) {
            Logger.e(TAG, "❌ WebRTC管理器初始化失败", e)
            isInitialized = false
            throw e
        }
    }

    /**
     * 连接到信令服务器
     */
    fun connectToSignalingServer() {
        Logger.i(TAG, "连接到信令服务器")

        if (!isInitialized) {
            Logger.e(TAG, "WebRTC管理器未初始化")
            return
        }

        if (isConnected) {
            Logger.w(TAG, "已经连接到信令服务器")
            return
        }

        // 检查是否正在连接中
        if (signalingClient?.isConnecting() == true) {
            Logger.w(TAG, "正在连接到信令服务器，跳过重复连接")
            return
        }

        // 获取信令服务器URL
        val signalingUrl = getSignalingUrl()

        // 连接到信令服务器
        signalingClient?.connect(signalingUrl)
    }

    /**
     * 发送退出消息并断开与信令服务器的连接
     */
    fun disconnectFromSignalingServerWithExitMessage() {
        Logger.i(TAG, "发送退出消息并断开与信令服务器的连接")

        if (!isConnected) {
            Logger.w(TAG, "未连接到信令服务器")
            return
        }

        // 关闭所有对等连接
        webRTCClient?.let { client ->
            // 获取所有对等连接ID
            val peerIds = signalingClient?.getConnectedPeers() ?: emptyList()

            // 关闭所有对等连接
            peerIds.forEach { peerId ->
                client.closePeerConnection(peerId)
            }
        }

        // 发送退出消息并断开连接
        signalingClient?.disconnectWithExitMessage()

        isConnected = false
        listener?.onSignalingDisconnected()
    }

    /**
     * 断开与信令服务器的连接
     */
    fun disconnectFromSignalingServer() {
        Logger.i(TAG, "断开与信令服务器的连接")

        if (!isConnected) {
            Logger.w(TAG, "未连接到信令服务器")
            return
        }

        // 永久断开连接（不再自动重连）
        signalingClient?.disconnectPermanently()

        // 关闭所有对等连接
        webRTCClient?.let { client ->
            // 获取所有对等连接ID
            val peerIds = signalingClient?.getConnectedPeers() ?: emptyList()

            // 关闭所有对等连接
            peerIds.forEach { peerId ->
                client.closePeerConnection(peerId)
            }
        }

        isConnected = false
        listener?.onSignalingDisconnected()
    }

    /**
     * 启动视频源
     */
    fun startVideoSource(context: Context) {
        Logger.i(TAG, "启动视频源: $currentVideoSource")

        // 检查是否正在启动中
        if (isVideoSourceStarting) {
            Logger.w(TAG, "视频源正在启动中，跳过重复请求")
            return
        }

        // 检查冷却时间（只有在之前有过启动操作时才检查）
        val currentTime = System.currentTimeMillis()
        if (lastVideoSourceStartTime > 0) {
            val timeSinceLastStart = currentTime - lastVideoSourceStartTime
            if (timeSinceLastStart < VIDEO_SOURCE_START_COOLDOWN) {
                val remainingTime = (VIDEO_SOURCE_START_COOLDOWN - timeSinceLastStart) / 1000
                Logger.w(TAG, "视频源启动冷却中，剩余时间: ${remainingTime}秒")
                return
            }
        }

        // 检查是否已经激活
        if (isVideoSourceActive && currentVideoSource == "camera") {
            Logger.w(TAG, "摄像头视频源已经激活，跳过重复启动")
            return
        }

        if (!isInitialized) {
            Logger.e(TAG, "WebRTC管理器未初始化，尝试重新初始化")
            // 尝试重新初始化
            try {
                if (context is WebRTCManagerListener) {
                    initialize(context, context)
                    Logger.i(TAG, "WebRTC管理器重新初始化成功")
                } else {
                    Logger.e(TAG, "无法重新初始化WebRTC管理器：context不是WebRTCManagerListener")
                    return
                }
            } catch (e: Exception) {
                Logger.e(TAG, "重新初始化WebRTC管理器失败", e)
                return
            }
        }

        // 设置启动状态
        isVideoSourceStarting = true
        lastVideoSourceStartTime = currentTime

        webRTCClient?.let { client ->
            try {
                when (currentVideoSource) {
                    "camera" -> {
                        // 创建摄像头视频源 - 同时设置本地和共享模式以保持兼容性
                        val cameraId = getCameraId()
                        Logger.i(TAG, "🎥 [摄像头启动] 开始创建摄像头视频源: $cameraId")

                        try {
                            val videoSource = client.createCameraVideoSource(cameraId)
                            client.setLocalVideoSource(videoSource)  // 保持原有兼容性
                            client.setSharedVideoSource(videoSource) // 启用共享模式优化

                            // 创建音频源 - 同时设置本地和共享模式
                            val audioSource = client.createAudioSource()
                            if (audioSource != null) {
                                client.setLocalAudioSource(audioSource)  // 保持原有兼容性
                                client.setSharedAudioSource(audioSource) // 启用共享模式优化
                            }

                            isVideoSourceActive = true
                            Logger.i(TAG, "摄像头视频源已启动 (共享模式): $cameraId")
                        } catch (e: Exception) {
                            Logger.w(TAG, "🎥 [摄像头启动] 摄像头启动失败，可能硬件未就绪: ${e.message}")

                            // 如果是硬件未就绪，安排延迟重试
                            if (e.message?.contains("未就绪") == true || e.message?.contains("not implemented") == true) {
                                Logger.i(TAG, "🎥 [摄像头启动] 安排5秒后重试摄像头启动")
                                isVideoSourceStarting = false // 重置启动状态，允许重试

                                // 延迟5秒后重试
                                Handler(Looper.getMainLooper()).postDelayed({
                                    Logger.i(TAG, "🎥 [摄像头启动] 执行延迟重试")
                                    startVideoSource(context)
                                }, 5000)
                                return
                            } else {
                                throw e // 其他错误直接抛出
                            }
                        }
                    }
                    "screen" -> {
                        // 检查是否有媒体投影
                        if (mediaProjection == null) {
                            Logger.e(TAG, "没有媒体投影，无法启动屏幕录制")
                            listener?.onError("没有媒体投影，无法启动屏幕录制")
                            isVideoSourceStarting = false
                            return
                        }

                        // 40秒等待已经足够，直接启动屏幕录制
                        Logger.i(TAG, "📱 [屏幕录制] 游戏已稳定40秒，直接启动屏幕录制")

                        // 启动服务
                        val intent = Intent(context, WebRTCSenderService::class.java).apply {
                            action = WebRTCSenderService.ACTION_START_SCREEN_CAPTURE
                        }
                        context.startService(intent)

                        isVideoSourceActive = true
                        Logger.i(TAG, "屏幕录制服务已启动")
                    }
                    "hdmiin" -> {
                        // 创建HDMI输入视频源
                        val devicePath = getHdmiDevicePath()
                        Logger.i(TAG, "🎥 [HDMI输入] 开始创建HDMI输入视频源: $devicePath")

                        try {
                            val videoSource = client.createHdmiVideoSource(devicePath)
                            client.setLocalVideoSource(videoSource)
                            client.setSharedVideoSource(videoSource)

                            // 创建音频源
                            val audioSource = client.createAudioSource()
                            if (audioSource != null) {
                                client.setLocalAudioSource(audioSource)
                                client.setSharedAudioSource(audioSource)
                            }

                            isVideoSourceActive = true
                            Logger.i(TAG, "HDMI输入视频源已启动: $devicePath")
                        } catch (e: Exception) {
                            Logger.w(TAG, "🎥 [HDMI输入] HDMI输入启动失败: ${e.message}")
                            throw e
                        }
                    }
                    "usbcapture" -> {
                        // 创建USB采集卡视频源
                        val devicePath = getUsbCaptureDevicePath()
                        Logger.i(TAG, "🎥 [USB采集卡] 开始创建USB采集卡视频源: $devicePath")

                        try {
                            val videoSource = client.createUsbCaptureVideoSource(devicePath)
                            client.setLocalVideoSource(videoSource)
                            client.setSharedVideoSource(videoSource)

                            // 创建音频源
                            val audioSource = client.createAudioSource()
                            if (audioSource != null) {
                                client.setLocalAudioSource(audioSource)
                                client.setSharedAudioSource(audioSource)
                            }

                            isVideoSourceActive = true
                            Logger.i(TAG, "USB采集卡视频源已启动: $devicePath")
                        } catch (e: Exception) {
                            Logger.w(TAG, "🎥 [USB采集卡] USB采集卡启动失败: ${e.message}")
                            throw e
                        }
                    }
                    else -> {
                        Logger.e(TAG, "未知的视频源类型: $currentVideoSource")
                        listener?.onError("未知的视频源类型: $currentVideoSource")
                        isVideoSourceStarting = false
                        return
                    }
                }

                isPaused = false
                isVideoSourceStarting = false
                listener?.onVideoSourceStarted(currentVideoSource)
            } catch (e: Exception) {
                Logger.e(TAG, "启动视频源失败", e)
                listener?.onError("启动视频源失败: ${e.message}")
                isVideoSourceStarting = false
                isVideoSourceActive = false

                // 处理视频源启动失败，检查是否需要自动重启
                handleVideoSourceFailure(context, e)
            }
        } ?: run {
            Logger.e(TAG, "WebRTC客户端为空")
            isVideoSourceStarting = false
        }
    }

    /**
     * 处理视频源启动失败
     */
    private fun handleVideoSourceFailure(context: Context, exception: Exception) {
        val currentTime = System.currentTimeMillis()

        // 如果距离上次失败超过重置间隔，重置失败计数
        if (currentTime - lastFailureTime > failureResetInterval) {
            videoSourceFailureCount = 0
            Logger.i(TAG, "🔄 重置视频源失败计数")
        }

        // 增加失败计数
        videoSourceFailureCount++
        lastFailureTime = currentTime

        Logger.w(TAG, "🚨 视频源启动失败计数: $videoSourceFailureCount/$maxFailureCount")

        // 记录失败日志
        DeviceLogManager.getInstance().logDeviceEvent(
            context,
            "video_source_failure",
            "ERROR",
            "视频源启动失败",
            "失败次数: $videoSourceFailureCount/$maxFailureCount, 错误: ${exception.message}, 视频源: $currentVideoSource"
        )

        // 检查是否达到重启阈值
        if (videoSourceFailureCount >= maxFailureCount) {
            Logger.e(TAG, "🚨 视频源连续失败${maxFailureCount}次，触发自动重启")

            // 触发自动重启
            AutoRebootManager.getInstance().triggerAutoReboot(
                context,
                "视频推流连续失败${maxFailureCount}次",
                "视频源: $currentVideoSource, 最后错误: ${exception.message}"
            )

            // 重置失败计数（避免重复触发）
            videoSourceFailureCount = 0
        }
    }

    /**
     * 停止视频源
     */
    fun stopVideoSource(context: Context) {
        Logger.i(TAG, "停止视频源")

        if (!isInitialized) {
            Logger.e(TAG, "WebRTC管理器未初始化")
            return
        }

        // 重置状态
        isVideoSourceStarting = false
        isVideoSourceActive = false

        // 先释放视频资源
        try {
            // 直接在这里实现视频资源释放逻辑，而不是调用WebRTCClient的方法
            webRTCClient?.let { client ->
                // 设置空视频源会触发资源释放
                client.setLocalVideoSource(null as org.webrtc.VideoSource?)
            }
        } catch (e: Exception) {
            Logger.e(TAG, "释放视频资源失败", e)
        }

        when (currentVideoSource) {
            "screen" -> {
                // 停止服务
                val intent = Intent(context, WebRTCSenderService::class.java).apply {
                    action = WebRTCSenderService.ACTION_STOP_SCREEN_CAPTURE
                }
                context.startService(intent)
            }
        }

        isPaused = true
        listener?.onVideoSourceStopped()
    }

    /**
     * 暂停视频源
     */
    fun pauseVideoSource() {
        Logger.i(TAG, "暂停视频源")

        if (!isInitialized || isPaused) {
            return
        }

        // 暂停视频源
        try {
            // 如果是屏幕录制，暂停捕获
            if (currentVideoSource == "screen") {
                // 通知服务暂停捕获
                // 这里可以添加暂停屏幕捕获的代码
            }

            // 设置暂停状态
            isPaused = true
            listener?.onVideoSourcePaused()

            // 广播暂停状态
            broadcastSourceInfo()
        } catch (e: Exception) {
            Logger.e(TAG, "暂停视频源失败", e)
        }
    }

    /**
     * 恢复视频源
     */
    fun resumeVideoSource() {
        Logger.i(TAG, "恢复视频源")

        if (!isInitialized || !isPaused) {
            return
        }

        // 恢复视频源
        try {
            // 如果是屏幕录制，恢复捕获
            if (currentVideoSource == "screen") {
                // 通知服务恢复捕获
                // 这里可以添加恢复屏幕捕获的代码
            }

            // 设置恢复状态
            isPaused = false
            listener?.onVideoSourceResumed()

            // 广播恢复状态
            broadcastSourceInfo()
        } catch (e: Exception) {
            Logger.e(TAG, "恢复视频源失败", e)
        }
    }

    /**
     * 设置媒体投影
     */
    fun setMediaProjection(mediaProjection: MediaProjection?) {
        this.mediaProjection = mediaProjection
    }

    /**
     * 获取媒体投影
     */
    fun getMediaProjection(): MediaProjection? {
        return mediaProjection
    }

    /**
     * 设置媒体投影数据
     */
    fun setMediaProjectionData(data: Intent?) {
        this.mediaProjectionData = data
    }

    /**
     * 获取媒体投影数据
     */
    fun getMediaProjectionData(): Intent? {
        return mediaProjectionData
    }

    /**
     * 设置视频源类型
     */
    fun setVideoSourceType(sourceType: String) {
        val validSources = listOf("camera", "screen", "hdmiin", "usbcapture")
        if (sourceType !in validSources) {
            Logger.e(TAG, "无效的视频源类型: $sourceType")
            return
        }

        currentVideoSource = sourceType

        // 保存到偏好设置
        WebRTCSenderApp.instance.preferences.edit()
            .putString(Constants.PREF_VIDEO_SOURCE, sourceType)
            .apply()
    }

    /**
     * 获取视频源类型
     */
    fun getVideoSourceType(): String {
        // 如果视频源未激活，返回"未启动"
        if (!isVideoSourceActive) {
            return "未启动"
        }

        // 返回当前实际运行的视频源类型
        return currentVideoSource
    }

    /**
     * 获取配置的视频源类型（从偏好设置）
     */
    fun getConfiguredVideoSourceType(): String {
        return WebRTCSenderApp.instance.preferences.getString(
            Constants.PREF_VIDEO_SOURCE,
            Constants.DEFAULT_VIDEO_SOURCE
        ) ?: Constants.DEFAULT_VIDEO_SOURCE
    }

    /**
     * 获取配置的游戏包名
     */
    fun getGamePackage(): String {
        return WebRTCSenderApp.instance.preferences.getString(
            Constants.PREF_AUTO_START_GAME_PACKAGE,
            ""
        ) ?: ""
    }

    /**
     * 检查是否有Unity游戏在运行
     */
    private fun isUnityGameRunning(): Boolean {
        return try {
            val activityManager = WebRTCSenderApp.instance.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningApps = activityManager.runningAppProcesses

            val unityApps = runningApps?.filter { processInfo ->
                processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND &&
                (processInfo.processName.contains("unity", ignoreCase = true) ||
                 processInfo.processName.contains("ocean3", ignoreCase = true) ||
                 processInfo.processName.contains("game", ignoreCase = true) ||
                 processInfo.processName.contains("ironnet", ignoreCase = true))
            }

            val hasUnityGame = !unityApps.isNullOrEmpty()
            if (hasUnityGame) {
                Logger.i(TAG, "📱 [图形检测] 检测到Unity游戏运行: ${unityApps?.map { it.processName }}")
            }

            hasUnityGame
        } catch (e: Exception) {
            Logger.w(TAG, "📱 [图形检测] 检查Unity游戏状态失败: ${e.message}")
            false
        }
    }

    /**
     * 设置摄像头ID
     */
    fun setCameraId(cameraId: String) {
        WebRTCSenderApp.instance.preferences.edit()
            .putString(Constants.PREF_CAMERA_ID, cameraId)
            .apply()
    }

    /**
     * 获取摄像头ID
     */
    fun getCameraId(): String {
        return WebRTCSenderApp.instance.preferences.getString(
            Constants.PREF_CAMERA_ID,
            Constants.DEFAULT_CAMERA_ID
        ) ?: Constants.DEFAULT_CAMERA_ID
    }

    /**
     * 检查视频源是否活跃
     */
    fun isVideoSourceActive(): Boolean {
        return isVideoSourceActive
    }



    /**
     * 设置HDMI设备路径
     */
    fun setHdmiDevicePath(devicePath: String) {
        WebRTCSenderApp.instance.preferences.edit()
            .putString(Constants.PREF_HDMI_DEVICE_PATH, devicePath)
            .apply()
    }

    /**
     * 获取HDMI设备路径
     */
    fun getHdmiDevicePath(): String {
        return WebRTCSenderApp.instance.preferences.getString(
            Constants.PREF_HDMI_DEVICE_PATH,
            Constants.DEFAULT_HDMI_DEVICE_PATH
        ) ?: Constants.DEFAULT_HDMI_DEVICE_PATH
    }

    /**
     * 设置USB采集卡设备路径
     */
    fun setUsbCaptureDevicePath(devicePath: String) {
        WebRTCSenderApp.instance.preferences.edit()
            .putString(Constants.PREF_USB_CAPTURE_DEVICE_PATH, devicePath)
            .apply()
    }

    /**
     * 获取USB采集卡设备路径
     */
    fun getUsbCaptureDevicePath(): String {
        return WebRTCSenderApp.instance.preferences.getString(
            Constants.PREF_USB_CAPTURE_DEVICE_PATH,
            Constants.DEFAULT_USB_CAPTURE_DEVICE_PATH
        ) ?: Constants.DEFAULT_USB_CAPTURE_DEVICE_PATH
    }

    /**
     * 设置视频分辨率
     */
    fun setVideoResolution(resolution: String) {
        if (!Constants.VIDEO_RESOLUTIONS.containsKey(resolution)) {
            Logger.e(TAG, "无效的视频分辨率: $resolution")
            return
        }

        WebRTCSenderApp.instance.preferences.edit()
            .putString(Constants.PREF_VIDEO_RESOLUTION, resolution)
            .apply()
    }

    /**
     * 获取视频分辨率
     */
    fun getVideoResolution(): String {
        return WebRTCSenderApp.instance.preferences.getString(
            Constants.PREF_VIDEO_RESOLUTION,
            Constants.DEFAULT_VIDEO_RESOLUTION
        ) ?: Constants.DEFAULT_VIDEO_RESOLUTION
    }

    /**
     * 设置视频比特率
     */
    fun setVideoBitrate(bitrate: Int) {
        WebRTCSenderApp.instance.preferences.edit()
            .putInt(Constants.PREF_VIDEO_BITRATE, bitrate)
            .apply()

        // 不需要立即更新编码参数，在创建Offer/Answer时会应用最新的设置
    }

    /**
     * 获取视频比特率
     */
    fun getVideoBitrate(): Int {
        return WebRTCSenderApp.instance.preferences.getInt(
            Constants.PREF_VIDEO_BITRATE,
            Constants.DEFAULT_VIDEO_BITRATE
        )
    }

    /**
     * 设置视频帧率
     */
    fun setVideoFramerate(framerate: Int) {
        WebRTCSenderApp.instance.preferences.edit()
            .putInt(Constants.PREF_VIDEO_FRAMERATE, framerate)
            .apply()

        // 不需要立即更新编码参数，在创建Offer/Answer时会应用最新的设置
    }

    /**
     * 获取视频帧率
     */
    fun getVideoFramerate(): Int {
        return WebRTCSenderApp.instance.preferences.getInt(
            Constants.PREF_VIDEO_FRAMERATE,
            Constants.DEFAULT_VIDEO_FRAMERATE
        )
    }

    /**
     * 获取可用摄像头列表
     */
    fun getAvailableCameras(context: Context): List<CameraInfo> {
        val cameras = mutableListOf<CameraInfo>()

        try {
            // 尝试Camera2
            val camera2Enumerator = Camera2Enumerator(context)
            val camera2DeviceNames = camera2Enumerator.deviceNames

            Logger.i(TAG, "🎥 [摄像头列表] Camera2可用设备: ${camera2DeviceNames.joinToString()}")

            for (deviceName in camera2DeviceNames) {
                try {
                    val isFrontFacing = camera2Enumerator.isFrontFacing(deviceName)
                    val isBackFacing = camera2Enumerator.isBackFacing(deviceName)

                    val displayName = when {
                        isFrontFacing -> "前置摄像头 ($deviceName)"
                        isBackFacing -> "后置摄像头 ($deviceName)"
                        else -> "摄像头 $deviceName"
                    }

                    cameras.add(CameraInfo(
                        id = deviceName,
                        displayName = displayName,
                        isFrontFacing = isFrontFacing,
                        isBackFacing = isBackFacing,
                        apiType = "Camera2"
                    ))
                } catch (e: Exception) {
                    Logger.w(TAG, "🎥 [摄像头列表] Camera2设备 $deviceName 信息获取失败: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [摄像头列表] Camera2枚举失败: ${e.message}")
        }

        // 如果Camera2没有找到摄像头，尝试Camera1
        if (cameras.isEmpty()) {
            try {
                val camera1Enumerator = Camera1Enumerator(false)
                val camera1DeviceNames = camera1Enumerator.deviceNames

                Logger.i(TAG, "🎥 [摄像头列表] Camera1可用设备: ${camera1DeviceNames.joinToString()}")

                for (deviceName in camera1DeviceNames) {
                    try {
                        val isFrontFacing = camera1Enumerator.isFrontFacing(deviceName)
                        val isBackFacing = camera1Enumerator.isBackFacing(deviceName)

                        val displayName = when {
                            isFrontFacing -> "前置摄像头 ($deviceName)"
                            isBackFacing -> "后置摄像头 ($deviceName)"
                            else -> "摄像头 $deviceName"
                        }

                        cameras.add(CameraInfo(
                            id = deviceName,
                            displayName = displayName,
                            isFrontFacing = isFrontFacing,
                            isBackFacing = isBackFacing,
                            apiType = "Camera1"
                        ))
                    } catch (e: Exception) {
                        Logger.w(TAG, "🎥 [摄像头列表] Camera1设备 $deviceName 信息获取失败: ${e.message}")
                    }
                }
            } catch (e: Exception) {
                Logger.e(TAG, "🎥 [摄像头列表] Camera1枚举失败: ${e.message}")
            }
        }

        Logger.i(TAG, "🎥 [摄像头列表] 总共找到 ${cameras.size} 个摄像头")
        cameras.forEach { camera ->
            Logger.i(TAG, "🎥 [摄像头列表] - ${camera.displayName} (ID: ${camera.id}, API: ${camera.apiType})")
        }

        return cameras
    }

    /**
     * 获取摄像头支持的分辨率列表
     */
    fun getCameraSupportedResolutions(context: Context, cameraId: String): List<Pair<Int, Int>> {
        val supportedResolutions = mutableListOf<Pair<Int, Int>>()

        try {
            Logger.i(TAG, "🎥 [分辨率查询] 查询摄像头 $cameraId 支持的分辨率")

            // 尝试Camera2
            val camera2Enumerator = Camera2Enumerator(WebRTCSenderApp.instance)
            val deviceNames = camera2Enumerator.deviceNames

            if (deviceNames.contains(cameraId)) {
                val supportedFormats = camera2Enumerator.getSupportedFormats(cameraId)

                if (supportedFormats != null) {
                    for (format in supportedFormats) {
                        val width = format.width
                        val height = format.height
                        supportedResolutions.add(Pair(width, height))
                        Logger.d(TAG, "🎥 [分辨率查询] 支持: ${width}x${height}")
                    }

                    // 按分辨率大小排序
                    supportedResolutions.sortByDescending { it.first * it.second }

                    Logger.i(TAG, "🎥 [分辨率查询] 摄像头 $cameraId 支持 ${supportedResolutions.size} 种分辨率")
                } else {
                    Logger.w(TAG, "🎥 [分辨率查询] 无法获取摄像头 $cameraId 的支持格式")
                }
            } else {
                Logger.w(TAG, "🎥 [分辨率查询] 摄像头 $cameraId 不在设备列表中")
            }

        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [分辨率查询] 查询摄像头分辨率失败", e)
        }

        return supportedResolutions
    }

    /**
     * 设置视频编解码器
     */
    fun setVideoCodec(codec: String) {
        WebRTCSenderApp.instance.preferences.edit()
            .putString(Constants.PREF_VIDEO_CODEC, codec)
            .apply()

        // 不需要立即更新编码参数，在创建Offer/Answer时会应用最新的设置
    }

    /**
     * 获取视频编解码器
     */
    fun getVideoCodec(): String {
        return WebRTCSenderApp.instance.preferences.getString(
            Constants.PREF_VIDEO_CODEC,
            Constants.DEFAULT_VIDEO_CODEC
        ) ?: Constants.DEFAULT_VIDEO_CODEC
    }

    /**
     * 设置音频来源
     */
    fun setAudioSource(source: String) {
        WebRTCSenderApp.instance.preferences.edit()
            .putString(Constants.PREF_AUDIO_SOURCE, source)
            .apply()
    }

    /**
     * 获取音频来源
     */
    fun getAudioSource(): String {
        val audioSource = WebRTCSenderApp.instance.preferences.getString(
            Constants.PREF_AUDIO_SOURCE,
            Constants.DEFAULT_AUDIO_SOURCE
        ) ?: Constants.DEFAULT_AUDIO_SOURCE

        Logger.d(TAG, "获取音频来源: $audioSource")
        return audioSource
    }

    /**
     * 设置音频通道
     */
    fun setAudioChannel(channel: String) {
        WebRTCSenderApp.instance.preferences.edit()
            .putString("audio_channel", channel)
            .apply()
    }

    /**
     * 获取音频通道
     */
    fun getAudioChannel(): String {
        return WebRTCSenderApp.instance.preferences.getString(
            "audio_channel",
            "stereo"
        ) ?: "stereo"
    }

    /**
     * 设置音频采样率
     */
    fun setAudioSampleRate(sampleRate: Int) {
        WebRTCSenderApp.instance.preferences.edit()
            .putInt("audio_sample_rate", sampleRate)
            .apply()
    }

    /**
     * 获取音频采样率
     */
    fun getAudioSampleRate(): Int {
        return WebRTCSenderApp.instance.preferences.getInt(
            "audio_sample_rate",
            48000
        )
    }

    /**
     * 设置音频编码
     */
    fun setAudioEncoding(encoding: String) {
        WebRTCSenderApp.instance.preferences.edit()
            .putString("audio_encoding", encoding)
            .apply()
    }

    /**
     * 获取音频编码
     */
    fun getAudioEncoding(): String {
        return WebRTCSenderApp.instance.preferences.getString(
            "audio_encoding",
            "pcm"
        ) ?: "pcm"
    }

    /**
     * 设置信令服务器URL
     */
    fun setSignalingUrl(url: String) {
        WebRTCSenderApp.instance.preferences.edit()
            .putString(Constants.PREF_SIGNALING_URL, url)
            .apply()
    }

    /**
     * 获取信令服务器URL
     */
    fun getSignalingUrl(): String {
        return WebRTCSenderApp.instance.preferences.getString(
            Constants.PREF_SIGNALING_URL,
            Constants.DEFAULT_SIGNALING_URL
        ) ?: Constants.DEFAULT_SIGNALING_URL
    }

    /**
     * 设置发送端ID
     */
    fun setSenderId(id: String) {
        WebRTCSenderApp.instance.preferences.edit()
            .putString(Constants.PREF_SENDER_ID, id)
            .apply()
    }

    /**
     * 获取发送端ID
     */
    fun getSenderId(): String {
        val savedId = WebRTCSenderApp.instance.preferences.getString(
            Constants.PREF_SENDER_ID,
            null
        )

        // 检查保存的ID是否是正确的格式（gamev-xxxxxxxx）
        if (savedId.isNullOrEmpty() || !savedId.startsWith("gamev-") || savedId.length != 13) {
            // 使用设备ID生成新的发送端ID
            val newId = DeviceUtils.generateSenderId(WebRTCSenderApp.instance)
            setSenderId(newId)
            Logger.i(TAG, "生成新的发送端ID: $newId (旧ID: $savedId)")
            return newId
        }

        Logger.d(TAG, "使用已保存的发送端ID: $savedId")
        return savedId
    }

    /**
     * 强制重新生成发送端ID
     */
    fun regenerateSenderId(): String {
        val newId = DeviceUtils.generateSenderId(WebRTCSenderApp.instance)
        setSenderId(newId)
        Logger.i(TAG, "强制重新生成发送端ID: $newId")
        return newId
    }

    /**
     * 获取SharedPreferences
     */
    fun getPreferences(): SharedPreferences = WebRTCSenderApp.instance.preferences

    /**
     * 关闭所有对等连接
     */
    fun closeAllPeerConnections() {
        Logger.i(TAG, "关闭所有对等连接")

        webRTCClient?.let { client ->
            // 获取所有对等连接ID
            val peerIds = signalingClient?.getConnectedPeers() ?: emptyList()

            // 关闭所有对等连接
            peerIds.forEach { peerId ->
                client.closePeerConnection(peerId)
                Logger.d(TAG, "已关闭对等连接: $peerId")
            }
        }
    }

    /**
     * 切换音频源
     */
    fun switchAudioSource(newAudioSource: String) {
        Logger.i(TAG, "🎵 [音频源切换] WebRTCManager请求切换音频源: $newAudioSource")

        try {
            webRTCClient?.switchAudioSource(newAudioSource)
        } catch (e: Exception) {
            Logger.e(TAG, "🎵 [音频源切换] 切换音频源失败", e)
        }
    }

    /**
     * 启动全面音频频道测试
     */
    fun startComprehensiveAudioTest() {
        Logger.i(TAG, "🎵 [音频测试] 启动全面音频频道测试...")

        try {
            webRTCClient?.let { client ->
                // 通过反射获取AudioSourceTester实例
                val audioSourceTesterField = client.javaClass.getDeclaredField("audioSourceTester")
                audioSourceTesterField.isAccessible = true
                val audioSourceTester = audioSourceTesterField.get(client)

                // 调用测试方法
                val testMethod = audioSourceTester.javaClass.getDeclaredMethod("startComprehensiveAudioChannelTest")
                testMethod.invoke(audioSourceTester)

                Logger.i(TAG, "🎵 [音频测试] 全面音频频道测试已启动")
            } ?: run {
                Logger.e(TAG, "🎵 [音频测试] WebRTC客户端未初始化，无法启动测试")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "🎵 [音频测试] 启动全面音频频道测试失败", e)
        }
    }

    /**
     * 启动基础音频源测试
     */
    fun startBasicAudioSourceTest() {
        Logger.i(TAG, "🎵 [音频测试] 启动基础音频源测试...")

        try {
            webRTCClient?.let { client ->
                // 通过反射获取AudioSourceTester实例
                val audioSourceTesterField = client.javaClass.getDeclaredField("audioSourceTester")
                audioSourceTesterField.isAccessible = true
                val audioSourceTester = audioSourceTesterField.get(client)

                // 调用测试方法
                val testMethod = audioSourceTester.javaClass.getDeclaredMethod("startAudioSourceTest")
                testMethod.invoke(audioSourceTester)

                Logger.i(TAG, "🎵 [音频测试] 基础音频源测试已启动")
            } ?: run {
                Logger.e(TAG, "🎵 [音频测试] WebRTC客户端未初始化，无法启动测试")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "🎵 [音频测试] 启动基础音频源测试失败", e)
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        Logger.i(TAG, "释放WebRTC管理器资源")

        try {
            // 发送退出消息并断开与信令服务器的连接
            disconnectFromSignalingServerWithExitMessage()

            // 关闭所有对等连接
            closeAllPeerConnections()

            // 释放WebRTC客户端资源
            webRTCClient?.release()
            webRTCClient = null

            // 释放信令客户端资源
            signalingClient = null

            // 🔧 修复：不停止MediaProjection，保持屏幕录制独立运行
            // 注释掉：mediaProjection?.stop()
            // 注释掉：mediaProjection = null
            Logger.i(TAG, "🎥 [屏幕录制独立] 保留MediaProjection，屏幕录制继续运行")

            isInitialized = false
            isConnected = false
            isPaused = false

            listener = null

            Logger.i(TAG, "WebRTC管理器资源已释放")
        } catch (e: Exception) {
            Logger.e(TAG, "释放WebRTC管理器资源失败", e)
        }
    }

    /**
     * 完全释放资源，包括停止屏幕录制
     */
    fun releaseCompletely() {
        Logger.i(TAG, "🎥 [完全释放] 停止屏幕录制并释放所有资源")

        try {
            // 停止MediaProjection
            mediaProjection?.stop()
            mediaProjection = null

            // 调用常规释放方法
            release()

            Logger.i(TAG, "🎥 [完全释放] 所有资源已释放，包括屏幕录制")
        } catch (e: Exception) {
            Logger.e(TAG, "完全释放资源失败", e)
        }
    }

    /**
     * 释放资源（带Context参数的重载方法，用于兼容旧代码）
     */
    fun release(context: Context) {
        // 停止视频源
        try {
            stopVideoSource(context)
        } catch (e: Exception) {
            Logger.e(TAG, "停止视频源失败", e)
        }

        // 调用无参数的release方法
        release()
    }

    /**
     * 获取活跃连接数
     */
    fun getActiveConnectionCount(): Int {
        return webRTCClient?.getActiveConnectionCount() ?: 0
    }



    /**
     * 是否已初始化
     */
    fun isInitialized(): Boolean {
        return isInitialized
    }

    /**
     * 是否已连接到信令服务器
     */
    fun isConnected(): Boolean {
        return isConnected
    }

    /**
     * 检查连接是否真正有效
     */
    fun isConnectionHealthy(): Boolean {
        return try {
            val healthy = isConnected && signalingClient?.let { client ->
                // 获取连接状态信息
                val connectionInfo = client.getConnectionInfo()
                Logger.d(TAG, "🔍 连接健康检查: $connectionInfo")

                // 检查并修复重连状态
                client.checkAndFixReconnectState()

                // 如果连接状态显示正常，返回true
                connectionInfo.contains("已连接")
            } ?: false

            // 如果连接不健康且没有在重连，尝试修复
            if (!healthy && !isConnected) {
                Logger.w(TAG, "🔍 连接不健康，检查重连状态")
                signalingClient?.checkAndFixReconnectState()
            }

            healthy
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 连接健康检查失败", e)
            false
        }
    }

    /**
     * 检查是否正在重连
     */
    fun isReconnecting(): Boolean {
        return signalingClient?.isReconnecting() == true
    }

    /**
     * 强制重新连接
     */
    fun forceReconnect() {
        Logger.i(TAG, "强制重新连接信令服务器")

        try {
            // 断开当前连接
            signalingClient?.disconnect()

            // 延迟1秒后重新连接
            Handler(Looper.getMainLooper()).postDelayed({
                connectToSignalingServer()
            }, 1000)
        } catch (e: Exception) {
            Logger.e(TAG, "强制重连失败", e)
        }
    }

    /**
     * 是否已暂停
     */
    fun isPaused(): Boolean {
        return isPaused
    }

    /**
     * 检查视频流状态
     */
    fun checkVideoStreamStatus(): String {
        try {
            Logger.i(TAG, "🎥 [视频流状态检查] ===== 开始检查视频流状态 =====")

            // 检查WebRTC管理器初始化状态
            if (!isInitialized) {
                Logger.e(TAG, "🎥 [视频流状态检查] ❌ WebRTC管理器未初始化")
                return "WebRTC管理器未初始化"
            }

            // 检查WebRTC客户端状态
            val client = webRTCClient
            if (client == null) {
                Logger.e(TAG, "🎥 [视频流状态检查] ❌ WebRTC客户端为空")
                return "WebRTC客户端为空"
            }

            // 检查视频源状态
            Logger.i(TAG, "🎥 [视频流状态检查] 当前视频源类型: $currentVideoSource")
            Logger.i(TAG, "🎥 [视频流状态检查] 视频源是否活跃: $isVideoSourceActive")
            Logger.i(TAG, "🎥 [视频流状态检查] 视频源是否暂停: $isPaused")
            Logger.i(TAG, "🎥 [视频流状态检查] 视频源是否正在启动: $isVideoSourceStarting")

            // 检查视频轨道状态
            val sharedTrack = client.getSharedVideoTrack()
            val localTrack = client.getLocalVideoTrack()

            Logger.i(TAG, "🎥 [视频流状态检查] 共享视频轨道: ${if (sharedTrack != null) "存在" else "不存在"}")
            Logger.i(TAG, "🎥 [视频流状态检查] 本地视频轨道: ${if (localTrack != null) "存在" else "不存在"}")

            val activeTrack = sharedTrack ?: localTrack
            if (activeTrack != null) {
                val trackState = activeTrack.state()
                val trackEnabled = activeTrack.enabled()
                Logger.i(TAG, "🎥 [视频流状态检查] 活跃视频轨道状态: $trackState")
                Logger.i(TAG, "🎥 [视频流状态检查] 活跃视频轨道启用: $trackEnabled")

                if (trackState != MediaStreamTrack.State.LIVE) {
                    Logger.e(TAG, "🎥 [视频流状态检查] ❌ 视频轨道状态异常: $trackState")
                    return "视频轨道状态异常: $trackState"
                }

                if (!trackEnabled) {
                    Logger.e(TAG, "🎥 [视频流状态检查] ❌ 视频轨道已禁用")
                    return "视频轨道已禁用"
                }
            } else {
                Logger.e(TAG, "🎥 [视频流状态检查] ❌ 没有可用的视频轨道")
                return "没有可用的视频轨道"
            }

            // 检查连接状态
            val connectionCount = client.getActiveConnectionCount()
            Logger.i(TAG, "🎥 [视频流状态检查] 活跃连接数: $connectionCount")

            // 检查信令服务器连接状态
            val signalingConnected = isConnected()
            Logger.i(TAG, "🎥 [视频流状态检查] 信令服务器连接: ${if (signalingConnected) "已连接" else "未连接"}")

            // 根据视频源类型进行特定检查
            when (currentVideoSource) {
                "camera" -> {
                    val cameraId = getCameraId()
                    Logger.i(TAG, "🎥 [视频流状态检查] 摄像头ID: $cameraId")

                    // 检查摄像头是否可用
                    try {
                        val cameraEnumerator = Camera2Enumerator(WebRTCSenderApp.instance)
                        val deviceNames = cameraEnumerator.deviceNames.toList()

                        if (deviceNames.contains(cameraId)) {
                            Logger.i(TAG, "🎥 [视频流状态检查] ✅ 摄像头可用")
                        } else {
                            Logger.e(TAG, "🎥 [视频流状态检查] ❌ 摄像头不可用")
                            return "摄像头不可用: $cameraId"
                        }
                    } catch (e: Exception) {
                        Logger.e(TAG, "🎥 [视频流状态检查] ❌ 检查摄像头失败", e)
                        return "检查摄像头失败: ${e.message}"
                    }
                }
                "screen" -> {
                    Logger.i(TAG, "🎥 [视频流状态检查] 屏幕录制模式")

                    // 检查MediaProjection状态
                    val mediaProjection = getMediaProjection()
                    if (mediaProjection == null) {
                        Logger.e(TAG, "🎥 [视频流状态检查] ❌ MediaProjection为空")
                        return "MediaProjection为空"
                    } else {
                        Logger.i(TAG, "🎥 [视频流状态检查] ✅ MediaProjection可用")
                    }
                }
            }

            Logger.i(TAG, "🎥 [视频流状态检查] ===== 视频流状态检查完成 =====")

            // 如果所有检查都通过
            if (isVideoSourceActive && !isPaused && activeTrack != null) {
                return "视频流正常运行"
            } else if (isPaused) {
                return "视频流已暂停"
            } else if (!isVideoSourceActive) {
                return "视频源未激活"
            } else {
                return "视频流状态未知"
            }

        } catch (e: Exception) {
            Logger.e(TAG, "🎥 [视频流状态检查] 检查失败", e)
            return "检查失败: ${e.message}"
        }
    }

    // WebRTCClient.WebRTCClientListener实现

    override fun onWebRTCClientInitialized() {
        Logger.i(TAG, "WebRTC客户端已初始化")
        listener?.onWebRTCInitialized()
    }

    override fun onLocalVideoSourceChanged(videoTrack: VideoTrack?) {
        Logger.i(TAG, "本地视频源已更改")
        listener?.onLocalVideoTrackAvailable(videoTrack)
    }

    override fun onIceCandidate(peerId: String, candidate: IceCandidate) {
        Logger.d(TAG, "ICE候选: $peerId")

        // 发送ICE候选到信令服务器
        signalingClient?.sendIceCandidate(peerId, candidate)
    }

    override fun onIceConnectionChange(peerId: String, state: IceConnectionState) {
        Logger.d(TAG, "ICE连接状态变化: $peerId, $state")

        when (state) {
            PeerConnection.IceConnectionState.CONNECTED -> {
                listener?.onPeerConnected(peerId)
            }
            PeerConnection.IceConnectionState.DISCONNECTED,
            PeerConnection.IceConnectionState.FAILED,
            PeerConnection.IceConnectionState.CLOSED -> {
                listener?.onPeerDisconnected(peerId)
            }
            else -> {
                // 忽略其他状态
            }
        }
    }

    override fun onOfferCreated(peerId: String, sdp: SessionDescription) {
        Logger.d(TAG, "Offer已创建: $peerId")

        // 发送Offer到信令服务器
        signalingClient?.sendOffer(peerId, sdp)
    }

    override fun onAnswerCreated(peerId: String, sdp: SessionDescription) {
        Logger.d(TAG, "Answer已创建: $peerId")

        // 发送Answer到信令服务器
        signalingClient?.sendAnswer(peerId, sdp)
    }

    override fun onCommandReceived(peerId: String, command: String, data: Map<*, *>) {
        Logger.d(TAG, "收到命令: $peerId, $command")

        when (command) {
            "pause" -> {
                pauseVideoSource()
            }
            "resume", "play" -> {
                resumeVideoSource()
            }
            "toggle_pause" -> {
                if (isPaused) {
                    resumeVideoSource()
                } else {
                    pauseVideoSource()
                }
            }
            "get_status" -> {
                // 不需要特殊处理，WebRTCClient已经发送了状态
            }
            "request_keyframe" -> {
                // 请求关键帧，WebRTCClient已经处理了具体逻辑
                Logger.d(TAG, "处理关键帧请求命令")
            }
            "adjust_video_quality" -> {
                // 调整视频质量，WebRTCClient已经处理了具体逻辑
                Logger.d(TAG, "处理视频质量调整命令")
            }
            else -> {
                Logger.w(TAG, "未知命令: $command")
            }
        }
    }

    override fun onPeerDisconnected(peerId: String) {
        Logger.i(TAG, "🧹 对等端 $peerId 已断开连接，开始清理")

        // 从观众列表中移除
        viewers.remove(peerId)

        // 确保连接完全清理
        webRTCClient?.closePeerConnection(peerId)

        // 通知UI
        listener?.onViewerLeft(peerId)
        listener?.onPeerDisconnected(peerId)

        Logger.i(TAG, "🧹 对等端 $peerId 清理完成，剩余观众: ${viewers.size}")
    }

    /**
     * 启动连接超时检查
     */
    private fun startConnectionTimeoutCheck() {
        connectionTimeoutHandler.postDelayed(object : Runnable {
            override fun run() {
                try {
                    val currentTime = System.currentTimeMillis()
                    val timeoutConnections = mutableListOf<String>()

                    // 检查超时连接
                    connectionTimeouts.forEach { (peerId, startTime) ->
                        if (currentTime - startTime > CONNECTION_TIMEOUT) {
                            timeoutConnections.add(peerId)
                        }
                    }

                    // 清理超时连接
                    timeoutConnections.forEach { peerId ->
                        Logger.w(TAG, "🧹 连接超时，强制清理: $peerId")
                        connectionTimeouts.remove(peerId)
                        webRTCClient?.forceCleanupAllConnections()
                        onPeerDisconnected(peerId)
                    }

                    // 继续检查
                    connectionTimeoutHandler.postDelayed(this, 10000) // 每10秒检查一次
                } catch (e: Exception) {
                    Logger.e(TAG, "连接超时检查失败", e)
                    connectionTimeoutHandler.postDelayed(this, 10000)
                }
            }
        }, 10000) // 10秒后开始第一次检查
    }

    // SignalingClientListener实现

    override fun onSignalingConnected() {
        // 检查是否为真正的重连（基于连接时间间隔）
        val currentTime = System.currentTimeMillis()
        val context = WebRTCSenderApp.instance
        val lastConnectionTime = context.getSharedPreferences("webrtc_state", Context.MODE_PRIVATE)
            .getLong("last_connection_time", 0)

        // 如果距离上次连接超过5分钟，认为是新的连接会话
        val isReconnection = hasBeenConnectedBefore && (currentTime - lastConnectionTime < 300000)

        Logger.i(TAG, "🔗 [信令连接] ✅ 已连接到信令服务器 ${if (isReconnection) "(重连)" else "(新连接)"}")

        isConnected = true
        hasBeenConnectedBefore = true

        // 记录连接时间
        context.getSharedPreferences("webrtc_state", Context.MODE_PRIVATE)
            .edit()
            .putLong("last_connection_time", currentTime)
            .apply()

        val senderId = getSenderId()
        Logger.i(TAG, "🔗 [信令连接] 发送端ID: $senderId")
        Logger.i(TAG, "🔗 [信令连接] 准备注册为视频源")

        // 注册发送端
        signalingClient?.register(senderId, "source", "", "")
        Logger.i(TAG, "🔗 [信令连接] 已发送注册请求")

        // 广播发送端ID
        broadcastSourceInfo()

        // 上报设备信息
        deviceInfoReporter?.let { reporter ->
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    if (isReconnection) {
                        // 重连成功 - 只上报一次，不启动定期上报
                        Logger.i(TAG, "🔗 [信令连接] 重连成功，上报设备信息")
                        reporter.onReconnectionEstablished(senderId)
                    } else {
                        // 首次连接成功 - 立即上报一次，然后启动定期上报
                        Logger.i(TAG, "🔗 [信令连接] 首次连接成功，上报设备信息并启动定期上报")
                        reporter.onConnectionEstablished(senderId)

                        // 开始定期上报（只在首次连接时启动，且会自动延迟避免重复）
                        reporter.startPeriodicReporting(senderId)
                    }

                    Logger.i(TAG, "🔗 [信令连接] 设备信息上报已${if (isReconnection) "重新" else ""}启动")
                } catch (e: Exception) {
                    Logger.e(TAG, "🔗 [信令连接] 设备信息上报失败", e)
                }
            }
        }

        listener?.onSignalingConnected()
    }

    /**
     * 广播发送端信息
     */
    fun broadcastSourceInfo() {
        if (!isConnected || signalingClient == null) {
            Logger.w(TAG, "未连接到信令服务器，无法广播发送端信息")
            return
        }

        // 检查广播冷却时间
        val currentTime = System.currentTimeMillis()
        if (lastBroadcastTime > 0) {
            val timeSinceLastBroadcast = currentTime - lastBroadcastTime
            if (timeSinceLastBroadcast < BROADCAST_COOLDOWN) {
                val remainingTime = (BROADCAST_COOLDOWN - timeSinceLastBroadcast) / 1000
                Logger.d(TAG, "广播冷却中，剩余时间: ${remainingTime}秒")
                return
            }
        }

        val senderId = getSenderId()
        Logger.i(TAG, "准备广播发送端ID: $senderId")

        // 更新最后广播时间
        lastBroadcastTime = currentTime

        // 创建广播消息
        val sourceInfo = mapOf(
            "type" to "source_info",
            "source_id" to senderId,
            "name" to "",
            "description" to "",
            "status" to if (isPaused) "ready" else "streaming"
        )

        val jsonMessage = gson.toJson(sourceInfo)
        Logger.i(TAG, "广播消息内容: $jsonMessage")

        try {
            // 向信令服务器发送广播消息
            signalingClient?.sendBroadcast(jsonMessage)
            Logger.i(TAG, "已广播发送端信息: $sourceInfo")
        } catch (e: Exception) {
            Logger.e(TAG, "广播发送端信息失败", e)
        }
    }

    override fun onSignalingDisconnected() {
        Logger.i(TAG, "已断开与信令服务器的连接")

        isConnected = false

        // 停止设备信息上报
        deviceInfoReporter?.stopPeriodicReporting()
        Logger.i(TAG, "🔗 [信令断开] 设备信息上报已停止")

        listener?.onSignalingDisconnected()
    }

    override fun onSignalingError(error: String) {
        Logger.e(TAG, "信令错误: $error")

        listener?.onError("信令错误: $error")
    }

    override fun onClientJoined(clientId: String) {
        Logger.i(TAG, "👥 [观众管理] 客户端加入: $clientId")

        // 记录连接开始时间
        connectionTimeouts[clientId] = System.currentTimeMillis()

        // � 修改：不主动创建PeerConnection，等待接收端发起连接
        Logger.i(TAG, "👥 [观众管理] 💡 新的WebRTC连接策略（接收端主动发起）:")
        Logger.i(TAG, "👥 [观众管理]   1. 接收端 $clientId 已连接到信令服务器 ✅")
        Logger.i(TAG, "👥 [观众管理]   2. 等待接收端主动发送Offer ⏳")
        Logger.i(TAG, "👥 [观众管理]   3. Android端创建PeerConnection并回复Answer")
        Logger.i(TAG, "👥 [观众管理]   4. ICE候选交换")
        Logger.i(TAG, "👥 [观众管理]   5. 连接建立")

        Logger.i(TAG, "👥 [观众管理] 🎯 优势：接收端主动发起连接成功率更高，减少NAT穿透问题")

        // 不再主动创建PeerConnection，等待onOfferReceived时创建
        // 这样可以让接收端主动发起连接，提高成功率

        listener?.onViewerJoined(clientId)
    }

    override fun onClientLeft(clientId: String) {
        Logger.i(TAG, "客户端离开: $clientId")

        // 关闭PeerConnection
        webRTCClient?.closePeerConnection(clientId)

        listener?.onViewerLeft(clientId)
    }

    override fun onOfferReceived(clientId: String, sdp: SessionDescription) {
        Logger.i(TAG, "📨 [信令接收] 收到来自 $clientId 的Offer")

        // 检查SDP是否为空
        if (sdp.description.isNullOrEmpty()) {
            Logger.e(TAG, "📨 [信令接收] ❌ 收到的Offer SDP为空")
            return
        }

        // 记录SDP内容的关键信息
        val sdpContent = sdp.description
        val hasVideo = sdpContent.contains("m=video")
        val hasAudio = sdpContent.contains("m=audio")
        val hasDataChannel = sdpContent.contains("m=application")

        Logger.i(TAG, "📨 [信令接收] Offer内容分析:")
        Logger.i(TAG, "📨 [信令接收]   - 包含视频: $hasVideo")
        Logger.i(TAG, "📨 [信令接收]   - 包含音频: $hasAudio")
        Logger.i(TAG, "📨 [信令接收]   - 包含数据通道: $hasDataChannel")
        Logger.i(TAG, "📨 [信令接收]   - SDP长度: ${sdpContent.length} 字符")
        // Logger.d(TAG, "📨 [信令接收] 完整SDP: $sdpContent")

        // 如果同一个clientId已有连接，先关闭旧连接（可能是重连）
        if (webRTCClient?.hasPeerConnection(clientId) == true) {
            Logger.i(TAG, "客户端 $clientId 已有连接，关闭旧连接以建立新连接")
            webRTCClient?.closePeerConnection(clientId)
        }

        // 检查PeerConnection是否存在，如果不存在则创建
        if (webRTCClient?.hasPeerConnection(clientId) != true) {
            Logger.i(TAG, "对等端 $clientId 的PeerConnection不存在，正在创建")

            // 创建PeerConnection
            val peerConnection = webRTCClient?.createPeerConnection(clientId)

            if (peerConnection == null) {
                Logger.e(TAG, "无法为对等端 $clientId 创建PeerConnection")
                return
            }

            Logger.i(TAG, "成功为对等端 $clientId 创建PeerConnection")
        } else {
            Logger.i(TAG, "对等端 $clientId 的PeerConnection已存在，重用现有连接")
        }

        try {
            // 设置远程Offer
            webRTCClient?.setRemoteOffer(clientId, sdp)

            // 向观众推送发送端ID（在Answer发送后）
            mainHandler.postDelayed({
                try {
                    val senderId = getSenderId()
                    val sourceInfo = mapOf(
                        "type" to "source_info",
                        "source_id" to senderId,
                        "name" to "",
                        "description" to ""
                    )
                    val success = webRTCClient?.sendDataChannelMessage(clientId, gson.toJson(sourceInfo)) ?: false
                    if (success) {
                        Logger.d(TAG, "向观众 $clientId 推送发送端ID: $senderId")
                    } else {
                        Logger.w(TAG, "向观众 $clientId 推送发送端ID失败，数据通道可能未打开")

                        // 如果数据通道未打开，检查连接状态
                        val pc = webRTCClient?.getPeerConnection(clientId)
                        if (pc != null) {
                            val iceState = pc.iceConnectionState()
                            val connState = pc.connectionState()
                            Logger.d(TAG, "连接状态: ICE=$iceState, Conn=$connState")

                            // 如果连接未建立，等待Answer创建完成，不要创建新的Offer
                            if (iceState != PeerConnection.IceConnectionState.CONNECTED &&
                                iceState != PeerConnection.IceConnectionState.COMPLETED &&
                                connState != PeerConnection.PeerConnectionState.CONNECTED) {
                                Logger.i(TAG, "连接正在建立中，等待Answer创建完成 (ICE=$iceState, Conn=$connState)")
                                // 注意：不要在这里创建Offer，因为我们已经收到了远程Offer，应该创建Answer
                            }
                        }
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "向观众推送发送端ID失败", e)
                }
            }, 3000) // 延迟3秒，确保数据通道有足够时间建立
        } catch (e: Exception) {
            Logger.e(TAG, "处理Offer失败", e)

            // 尝试重新创建PeerConnection
            mainHandler.postDelayed({
                Logger.i(TAG, "处理Offer失败，尝试重新创建PeerConnection")

                // 关闭旧的PeerConnection
                webRTCClient?.closePeerConnection(clientId)

                // 创建新的PeerConnection
                val peerConnection = webRTCClient?.createPeerConnection(clientId)

                if (peerConnection != null) {
                    // 尝试再次设置远程描述
                    try {
                        webRTCClient?.setRemoteOffer(clientId, sdp)
                    } catch (e: Exception) {
                        Logger.e(TAG, "重新设置远程描述失败", e)
                    }
                }
            }, 1000) // 延迟1秒再尝试
        }
    }

    override fun onAnswerReceived(clientId: String, sdp: SessionDescription) {
        Logger.d(TAG, "收到Answer: $clientId")

        // 检查SDP是否为空
        if (sdp.description.isNullOrEmpty()) {
            Logger.e(TAG, "收到的Answer SDP为空")
            return
        }

        // 记录SDP内容
        Logger.d(TAG, "Answer SDP: ${sdp.description}")

        // 检查PeerConnection是否存在
        if (webRTCClient?.hasPeerConnection(clientId) != true) {
            Logger.e(TAG, "对等端 $clientId 的PeerConnection不存在，无法设置远程Answer")
            return
        }

        try {
            // 设置远程Answer
            webRTCClient?.setRemoteAnswer(clientId, sdp)

            // 设置连接状态检查
            mainHandler.postDelayed({
                val pc = webRTCClient?.getPeerConnection(clientId)
                if (pc != null) {
                    val iceState = pc.iceConnectionState()
                    val connState = pc.connectionState()
                    Logger.d(TAG, "连接状态: ICE=$iceState, Conn=$connState")

                    // 如果连接未建立，等待Answer创建完成，不要创建新的Offer
                    if (iceState != PeerConnection.IceConnectionState.CONNECTED &&
                        iceState != PeerConnection.IceConnectionState.COMPLETED &&
                        connState != PeerConnection.PeerConnectionState.CONNECTED) {
                        Logger.i(TAG, "连接正在建立中，等待Answer创建完成 (ICE=$iceState, Conn=$connState)")
                        // 注意：不要在这里创建Offer，因为我们已经收到了远程Offer，应该创建Answer
                    }
                }
            }, 3000) // 3秒后检查连接状态
        } catch (e: Exception) {
            Logger.e(TAG, "设置远程Answer失败", e)
        }
    }

    override fun onIceCandidateReceived(clientId: String, candidate: IceCandidate) {
        Logger.d(TAG, "收到ICE候选: $clientId")

        // 检查PeerConnection是否存在，如果不存在则创建
        if (webRTCClient?.hasPeerConnection(clientId) != true) {
            Logger.i(TAG, "对等端 $clientId 的PeerConnection不存在，正在创建")

            // 创建PeerConnection
            val peerConnection = webRTCClient?.createPeerConnection(clientId)

            if (peerConnection == null) {
                Logger.e(TAG, "无法为对等端 $clientId 创建PeerConnection")
                return
            }

            Logger.i(TAG, "成功为对等端 $clientId 创建PeerConnection")

            // 注意：不要在这里创建Offer，因为收到ICE候选说明对方已经发送了Offer
            // 我们应该等待onOfferReceived被调用，然后创建Answer
            Logger.i(TAG, "为对等端 $clientId 创建PeerConnection完成，等待Offer到达")

            // 设置连接超时检查
            mainHandler.postDelayed({
                val pc = webRTCClient?.getPeerConnection(clientId)
                if (pc != null) {
                    val iceState = pc.iceConnectionState()
                    val connState = pc.connectionState()
                    Logger.d(TAG, "连接状态检查: ICE=$iceState, Conn=$connState")

                    if (iceState != PeerConnection.IceConnectionState.CONNECTED &&
                        iceState != PeerConnection.IceConnectionState.COMPLETED &&
                        connState != PeerConnection.PeerConnectionState.CONNECTED) {
                        Logger.i(TAG, "连接正在建立中，等待Answer创建完成 (ICE=$iceState, Conn=$connState)")
                        // 注意：不要在这里创建Offer，因为我们已经收到了远程Offer，应该创建Answer
                    }
                }
            }, 5000) // 5秒后检查连接状态

            // 向观众推送发送端ID
            mainHandler.postDelayed({
                try {
                    val senderId = getSenderId()
                    val sourceInfo = mapOf(
                        "type" to "source_info",
                        "source_id" to senderId,
                        "name" to "",
                        "description" to ""
                    )
                    val success = webRTCClient?.sendDataChannelMessage(clientId, gson.toJson(sourceInfo)) ?: false
                    if (success) {
                        Logger.d(TAG, "向观众 $clientId 推送发送端ID: $senderId")
                    } else {
                        Logger.w(TAG, "向观众 $clientId 推送发送端ID失败，数据通道可能未打开")

                        // 如果数据通道未打开，等待连接完成，不要创建新的Offer
                        Logger.i(TAG, "数据通道未打开，等待Answer创建完成和连接建立")
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "向观众推送发送端ID失败", e)
                }
            }, 2000) // 延迟2秒，确保数据通道有足够时间建立
        }

        // 添加ICE候选
        webRTCClient?.addIceCandidate(clientId, candidate)
    }

    /**
     * WebRTC管理器监听器接口
     */
    interface WebRTCManagerListener {
        fun onWebRTCInitialized()
        fun onSignalingConnected()
        fun onSignalingDisconnected()
        fun onLocalVideoTrackAvailable(videoTrack: VideoTrack?)
        fun onVideoSourceStarted(sourceType: String)
        fun onVideoSourceStopped()
        fun onVideoSourcePaused()
        fun onVideoSourceResumed()
        fun onViewerJoined(viewerId: String)
        fun onViewerLeft(viewerId: String)
        fun onPeerConnected(peerId: String)
        fun onPeerDisconnected(peerId: String)
        fun onError(message: String)
    }



    /**
     * 摄像头信息数据类
     */
    data class CameraInfo(
        val id: String,
        val displayName: String,
        val isFrontFacing: Boolean,
        val isBackFacing: Boolean,
        val apiType: String
    )

}
