package com.example.webrtcsender.ui

import android.Manifest
import android.app.Activity
import android.app.ActivityManager
import android.content.*
import android.content.pm.PackageManager
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import org.webrtc.RendererCommon
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.example.webrtcsender.R
import com.example.webrtcsender.service.WebRTCSenderService
import com.example.webrtcsender.utils.Constants
import com.example.webrtcsender.utils.GameLauncher
import com.example.webrtcsender.utils.Logger
import com.example.webrtcsender.webrtc.WebRTCClient
import com.example.webrtcsender.webrtc.WebRTCManager
import org.webrtc.SurfaceViewRenderer
import org.webrtc.VideoTrack
import java.io.File

class MainActivity : AppCompatActivity(), WebRTCManager.WebRTCManagerListener {
    companion object {
        private const val TAG = "MainActivity"
    }

    // UI组件
    private lateinit var localVideoView: SurfaceViewRenderer
    private lateinit var statusText: TextView
    private lateinit var connectionText: TextView
    private lateinit var senderIdText: TextView
    private lateinit var videoSourceText: TextView
    private lateinit var startButton: Button
    private lateinit var stopButton: Button
    private lateinit var cameraButton: Button
    private lateinit var screenButton: Button
    private lateinit var hdmiButton: Button
    private lateinit var usbCaptureButton: Button
    private lateinit var checkStatusButton: Button
    private lateinit var settingsButton: Button



    // 服务连接
    private var service: WebRTCSenderService? = null
    private var isBound = false

    // 优化启动流程控制
    private var videoSourceStartupCompleted = false
    private var isServiceStarting = false
    private var lastServiceStartTime = 0L
    private var disableCameraRetryUntil = 0L  // 禁用摄像头重试直到指定时间

    // 媒体投影
    private lateinit var mediaProjectionManager: MediaProjectionManager

    // 广播接收器
    private lateinit var serviceStatusReceiver: BroadcastReceiver
    private lateinit var connectionStatusReceiver: BroadcastReceiver

    // 服务连接
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, binder: IBinder) {
            val localBinder = binder as WebRTCSenderService.LocalBinder
            service = localBinder.getService()
            isBound = true

            updateUI()

            // 启动摄像头重试定时器
            startCameraRetryTimer()
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            service = null
            isBound = false

            // 停止摄像头重试定时器
            stopCameraRetryTimer()
        }
    }

    // 请求码
    private val SCREEN_CAPTURE_REQUEST_CODE = 1001
    private val PERMISSIONS_REQUEST_CODE = 1002

    // 需要的权限
    private val requiredPermissions = arrayOf(
        Manifest.permission.CAMERA,
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.READ_EXTERNAL_STORAGE
    )

    // 防止重复切换的标志
    private var isCameraSwitching = false
    private var lastCameraSwitchTime = 0L
    private val CAMERA_SWITCH_COOLDOWN = 5000L // 5秒冷却时间

    // 摄像头重试相关变量
    private var cameraRetryCount = 0
    private val MAX_CAMERA_RETRY = 3 // 最大重试次数
    private var lastCameraRetryTime = 0L
    private var cameraRetryHandler: Handler? = null
    private var cameraRetryRunnable: Runnable? = null

    // 优化启动流程状态
    private var isOptimizedLaunchInProgress = false

    // 自动启动标志 - 首次安装或基于用户历史操作
    private fun shouldAutoStartService(): Boolean {
        val prefs = WebRTCManager.getPreferences()
        val hasManualOperation = prefs.getBoolean(Constants.PREF_HAS_MANUAL_OPERATION, false)
        val isFirstInstall = prefs.getBoolean(Constants.PREF_FIRST_INSTALL, Constants.DEFAULT_FIRST_INSTALL)

        // 首次安装时自动启动，或者有过手动操作历史时自动启动
        return isFirstInstall || hasManualOperation
    }

    private fun shouldAutoStartCapture(): Boolean {
        val prefs = WebRTCManager.getPreferences()
        val hasManualOperation = prefs.getBoolean(Constants.PREF_HAS_MANUAL_OPERATION, false)
        val isFirstInstall = prefs.getBoolean(Constants.PREF_FIRST_INSTALL, Constants.DEFAULT_FIRST_INSTALL)

        // 首次安装时自动启动，或者有过手动操作历史时自动启动
        return isFirstInstall || hasManualOperation
    }



    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        // 设置ActionBar标题显示版本信息
        setupActionBarTitle()

        // 清理遗留的APK文件
        cleanupLeftoverApkFiles()

        // 初始化UI组件
        localVideoView = findViewById(R.id.localVideoView)
        statusText = findViewById(R.id.statusText)
        connectionText = findViewById(R.id.connectionText)
        senderIdText = findViewById(R.id.senderIdText)
        videoSourceText = findViewById(R.id.videoSourceText)
        startButton = findViewById(R.id.startButton)
        stopButton = findViewById(R.id.stopButton)
        cameraButton = findViewById(R.id.cameraButton)
        screenButton = findViewById(R.id.screenButton)
        hdmiButton = findViewById(R.id.hdmiButton)
        usbCaptureButton = findViewById(R.id.usbCaptureButton)
        checkStatusButton = findViewById(R.id.checkStatusButton)
        settingsButton = findViewById(R.id.settingsButton)



        // 初始化媒体投影管理器
        mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager

        // 初始化广播接收器
        initBroadcastReceivers()

        // 初始化UI
        initUI()

        // 启动定期状态更新
        startPeriodicStatusUpdate()

        // 检查权限
        if (checkPermissions()) {
            // 初始化WebRTC
            initWebRTC()

            // 应用启动时禁用摄像头重试1分钟，优先让优化启动流程执行
        disableCameraRetryUntil = System.currentTimeMillis() + 60000
        Logger.i(TAG, "🚀 [应用启动] 禁用摄像头重试机制3分钟，优先执行优化启动流程")

        // 检查是否应该自动启动（首次安装或基于用户历史操作）
            if (shouldAutoStartService()) {
                val prefs = WebRTCManager.getPreferences()
                val isFirstInstall = prefs.getBoolean(Constants.PREF_FIRST_INSTALL, Constants.DEFAULT_FIRST_INSTALL)
                val hasManualOperation = prefs.getBoolean(Constants.PREF_HAS_MANUAL_OPERATION, false)

                if (isFirstInstall) {
                    Logger.i(TAG, "🎉 首次安装检测，自动启动服务和摄像头")
                } else if (hasManualOperation) {
                    Logger.i(TAG, "检测到用户历史操作，开始优化启动流程")
                }

                startOptimizedLaunchSequence()
            } else {
                Logger.i(TAG, "无需自动启动")
            }
        } else {
            // 请求权限
            requestPermissions()
        }
    }

    override fun onStart() {
        super.onStart()

        // 绑定服务
        bindService()

        // 注册广播接收器
        registerReceivers()

        // 更新连接状态
        updateConnectionStatus()

        // 在优化启动流程期间，不进行视频渲染恢复
        // 避免干扰启动时序
        Logger.d(TAG, "onStart - 跳过视频渲染恢复，等待优化启动流程")
    }

    /**
     * 优化的启动流程：先启动前台服务，立即启动游戏，然后启动视频源
     */
    private fun startOptimizedLaunchSequence() {
        Logger.i(TAG, "🚀 [优化启动] 开始优化启动流程")

        // 设置启动流程标志位
        isOptimizedLaunchInProgress = true

        // 立即禁用摄像头重试机制，避免冲突
        disableCameraRetryUntil = System.currentTimeMillis() + 60000  // 1分钟
        Logger.i(TAG, "🚀 [优化启动] 立即禁用摄像头重试机制3分钟，避免冲突")

        // 第一步：在应用还在前台时先启动前台服务（避免后台启动限制）
        Logger.i(TAG, "🚀 [优化启动] 步骤1: 先启动前台服务（避免后台启动限制）")
        startServiceDirectly()

        // 第二步：立即启动配置的游戏（不等待视频流）
        Logger.i(TAG, "🚀 [优化启动] 步骤2: 立即启动配置的游戏")
        startConfiguredGameImmediately()

        // 第三步：启动视频源
        Logger.i(TAG, "🚀 [优化启动] 步骤3: 启动视频源")

        if (shouldAutoStartCapture()) {
            // 使用配置的视频源类型，而不是当前运行状态
            val lastVideoSource = WebRTCManager.getConfiguredVideoSourceType()
            Logger.i(TAG, "🚀 [优化启动] 启动视频源: $lastVideoSource")

            when (lastVideoSource) {
                "screen" -> {
                    Logger.i(TAG, "🚀 [优化启动] 启动屏幕录制")
                    requestScreenCapture()

                    // 屏幕录制启动后立即完成启动流程
                    Logger.i(TAG, "🚀 [优化启动] ✅ 优化启动流程完成 - 屏幕录制已启动")

                    // 延迟3秒清除启动流程标志位，给屏幕录制一些初始化时间
                    Handler(mainLooper).postDelayed({
                        Logger.i(TAG, "🚀 [优化启动] 清除启动流程标志位")
                        isOptimizedLaunchInProgress = false
                    }, 3000)
                }
                "camera" -> {
                    Logger.i(TAG, "🚀 [优化启动] 启动摄像头")
                    startCameraCaptureDirectly()

                    // 摄像头启动后立即完成启动流程
                    Logger.i(TAG, "🚀 [优化启动] ✅ 优化启动流程完成 - 摄像头已启动")

                    // 延迟3秒清除启动流程标志位，给摄像头一些初始化时间
                    Handler(mainLooper).postDelayed({
                        Logger.i(TAG, "🚀 [优化启动] 清除启动流程标志位")
                        isOptimizedLaunchInProgress = false

                        // 禁用摄像头重试机制1分钟，避免与启动流程冲突
                        disableCameraRetryUntil = System.currentTimeMillis() + 60000
                        Logger.i(TAG, "🚀 [优化启动] 禁用摄像头重试机制1分钟，避免冲突")
                    }, 3000)
                }
                "hdmiin" -> {
                    Logger.i(TAG, "🚀 [优化启动] 启动HDMI输入")
                    startHdmiCapture()

                    // HDMI输入启动后立即完成启动流程
                    Logger.i(TAG, "🚀 [优化启动] ✅ 优化启动流程完成 - HDMI输入已启动")

                    // 延迟3秒清除启动流程标志位，给HDMI输入一些初始化时间
                    Handler(mainLooper).postDelayed({
                        Logger.i(TAG, "🚀 [优化启动] 清除启动流程标志位")
                        isOptimizedLaunchInProgress = false
                    }, 3000)
                }
                "usbcapture" -> {
                    Logger.i(TAG, "🚀 [优化启动] 启动USB采集卡")
                    startUsbCapture()

                    // USB采集卡启动后立即完成启动流程
                    Logger.i(TAG, "🚀 [优化启动] ✅ 优化启动流程完成 - USB采集卡已启动")

                    // 延迟3秒清除启动流程标志位，给USB采集卡一些初始化时间
                    Handler(mainLooper).postDelayed({
                        Logger.i(TAG, "🚀 [优化启动] 清除启动流程标志位")
                        isOptimizedLaunchInProgress = false
                    }, 3000)
                }
                else -> {
                    Logger.i(TAG, "🚀 [优化启动] 无有效的历史记录，不自动启动视频源")
                    isOptimizedLaunchInProgress = false // 清除标志位
                }
            }

        } else {
            Logger.i(TAG, "🚀 [优化启动] 不需要自动启动视频源")
            isOptimizedLaunchInProgress = false // 清除标志位
        }
    }

    /**
     * 立即启动配置的游戏（不等待视频流）
     */
    private fun startConfiguredGameImmediately() {
        try {
            val gamePackage = WebRTCManager.getGamePackage()
            if (gamePackage.isNotEmpty()) {
                Logger.i(TAG, "🎮 [游戏启动] 立即启动配置的游戏: $gamePackage")

                // 延迟2秒启动游戏，给服务一些启动时间
                Handler(mainLooper).postDelayed({
                    GameLauncher.launchGame(this@MainActivity, gamePackage)
                    Logger.i(TAG, "🎮 [游戏启动] ✅ 游戏已启动")
                }, 2000)
            } else {
                Logger.i(TAG, "🎮 [游戏启动] 未配置游戏包名，跳过游戏启动")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "🎮 [游戏启动] 启动游戏失败", e)
        }
    }

    override fun onResume() {
        super.onResume()

        // 在优化启动流程期间，不进行视频渲染操作
        // 避免干扰启动时序
        Logger.d(TAG, "onResume - 跳过视频渲染操作，等待优化启动流程")
    }

    override fun onStop() {
        super.onStop()

        // 在优化启动流程期间，不进行任何操作，避免干扰启动时序
        if (isOptimizedLaunchInProgress) {
            Logger.d(TAG, "onStop - 优化启动流程进行中，跳过所有操作")
            return
        }

        // 解绑服务
        unbindService()

        // 注销广播接收器
        unregisterReceivers()

        // 暂停视频渲染（但不暂停视频源，特别是屏幕录制）
        try {
            Logger.d(TAG, "暂停视频渲染（保持视频源运行）")

            // 只暂停本地视频显示，不影响推流
            localVideoView.visibility = View.GONE
            try {
                // 获取本地视频轨道
                val videoTrack = getLocalVideoTrack()
                if (videoTrack != null) {
                    videoTrack.removeSink(localVideoView)
                    Logger.d(TAG, "已移除视频轨道的sink")
                }
            } catch (e: Exception) {
                Logger.e(TAG, "移除视频轨道的sink失败", e)
            }

            // 注意：不调用 WebRTCManager.pauseVideoSource()
            // 因为屏幕录制等视频源应该在后台继续运行
            Logger.d(TAG, "视频源保持运行，仅暂停本地显示")
        } catch (e: Exception) {
            Logger.e(TAG, "暂停视频渲染失败", e)
        }
    }

    override fun onDestroy() {
        super.onDestroy()

        // 停止摄像头重试定时器
        stopCameraRetryTimer()

        // 释放视频渲染器
        localVideoView.release()
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_settings -> {
                // 打开设置界面
                startActivity(Intent(this, SettingsActivity::class.java))
                true
            }
            
    /**
            R.id.action_switch_to_system_audio -> {
                // 切换到系统音频 (REMOTE_SUBMIX)
                Logger.i(TAG, "🎵 [菜单] 用户请求切换到系统音频")
                WebRTCManager.switchAudioSource("remote_submix")
                Toast.makeText(this, "🎵 已切换到系统音频 (REMOTE_SUBMIX)", Toast.LENGTH_SHORT).show()
                true
            }
            R.id.action_switch_to_microphone -> {
                // 切换到麦克风
                Logger.i(TAG, "🎵 [菜单] 用户请求切换到麦克风")
                WebRTCManager.switchAudioSource("mic")
                Toast.makeText(this, "🎤 已切换到麦克风", Toast.LENGTH_SHORT).show()
                true
            }

     */
            else -> super.onOptionsItemSelected(item)
        }
    }

    /**
     * 初始化广播接收器
     */
    private fun initBroadcastReceivers() {
        // 服务状态接收器
        serviceStatusReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val status = intent.getStringExtra(WebRTCSenderService.EXTRA_SERVICE_STATUS)
                val errorMessage = intent.getStringExtra(WebRTCSenderService.EXTRA_ERROR_MESSAGE)

                Logger.d(TAG, "收到服务状态广播: $status, $errorMessage")

                when (status) {
                    "running" -> {
                        statusText.text = "服务已启动"
                        startButton.isEnabled = false
                        stopButton.isEnabled = true
                        cameraButton.isEnabled = true
                        screenButton.isEnabled = true
                    }
                    "stopped" -> {
                        statusText.text = "服务已停止"
                        startButton.isEnabled = true
                        stopButton.isEnabled = false
                        cameraButton.isEnabled = false
                        screenButton.isEnabled = false
                    }
                    "capturing" -> {
                        statusText.text = "正在推流"
                    }
                    "stopped_capture" -> {
                        statusText.text = "已停止推流"
                    }
                    "error" -> {
                        statusText.text = "错误: $errorMessage"
                        showError(errorMessage ?: "未知错误")
                    }
                    "media_projection_stopped" -> {
                        statusText.text = "⚠️ 屏幕录制被中断（scrcpy等应用关闭）"
                        Toast.makeText(this@MainActivity, "检测到屏幕录制中断，正在尝试恢复...", Toast.LENGTH_LONG).show()
                    }
                    "screen_capture_recovered" -> {
                        statusText.text = "✅ 屏幕录制已自动恢复"
                        Toast.makeText(this@MainActivity, "屏幕录制已自动恢复", Toast.LENGTH_SHORT).show()
                    }
                    "camera_capture_recovered" -> {
                        statusText.text = "✅ 摄像头已自动恢复"
                        Toast.makeText(this@MainActivity, "摄像头已自动恢复", Toast.LENGTH_SHORT).show()
                    }
                    "recovery_failed" -> {
                        statusText.text = "❌ 自动恢复失败"
                        showScreenCaptureRecoveryDialog(errorMessage ?: "恢复失败")
                    }
                }
            }
        }

        // 连接状态接收器
        connectionStatusReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                val status = intent.getStringExtra(WebRTCSenderService.EXTRA_CONNECTION_STATUS)
                val viewerCount = intent.getIntExtra(WebRTCSenderService.EXTRA_VIEWER_COUNT, 0)

                Logger.d(TAG, "收到连接状态广播: $status, $viewerCount")

                when (status) {
                    "connected" -> {
                        connectionText.text = "已连接到信令服务器"
                    }
                    "streaming" -> {
                        connectionText.text = "正在推流，$viewerCount 个观众"
                    }
                    "disconnected" -> {
                        connectionText.text = "未连接到信令服务器"
                    }
                }
            }
        }
    }

    /**
     * 注册广播接收器
     */
    private fun registerReceivers() {
        // 注册服务状态接收器
        registerReceiver(
            serviceStatusReceiver,
            IntentFilter(WebRTCSenderService.ACTION_SERVICE_STATUS)
        )

        // 注册连接状态接收器
        registerReceiver(
            connectionStatusReceiver,
            IntentFilter(WebRTCSenderService.ACTION_CONNECTION_STATUS)
        )
    }

    /**
     * 注销广播接收器
     */
    private fun unregisterReceivers() {
        try {
            unregisterReceiver(serviceStatusReceiver)
        } catch (e: Exception) {
            Logger.e(TAG, "注销服务状态接收器失败", e)
        }

        try {
            unregisterReceiver(connectionStatusReceiver)
        } catch (e: Exception) {
            Logger.e(TAG, "注销连接状态接收器失败", e)
        }
    }

    /**
     * 初始化UI
     */
    private fun initUI() {
        // 设置标题
        title = "线上线下服务台"

        // 初始化视频渲染器
        initVideoRenderer()

        // 设置按钮点击事件
        startButton.setOnClickListener {
            startService()
        }

        stopButton.setOnClickListener {
            stopService()
        }

        cameraButton.setOnClickListener {
            startCameraCaptureWithServiceRestart()
        }

        screenButton.setOnClickListener {
            requestScreenCapture()
        }

        hdmiButton.setOnClickListener {
            startHdmiCapture()
        }

        usbCaptureButton.setOnClickListener {
            startUsbCapture()
        }

        checkStatusButton.setOnClickListener {
            checkVideoStreamStatus()
        }

        settingsButton.setOnClickListener {
            openSettings()
        }





        // 初始状态
        startButton.isEnabled = true
        stopButton.isEnabled = false
        cameraButton.isEnabled = false
        screenButton.isEnabled = false

        statusText.text = "服务未启动"

        // 检查当前连接状态
        updateConnectionStatus()

        // 更新视频源状态
        updateVideoSourceStatus()

        // 显示发送端ID（强制重新生成以确保使用新的格式）
        val senderId = WebRTCManager.regenerateSenderId()
        senderIdText.text = "发送端ID: $senderId"
    }

    /**
     * 初始化视频渲染器
     */
    private fun initVideoRenderer() {
        try {
            // 使用WebRTCClient的EglContext初始化
            val eglContext = WebRTCManager.webRTCClient?.getEglContext()
            Logger.d(TAG, "初始化视频渲染器，EglContext: $eglContext")

            localVideoView.init(eglContext, null)
            localVideoView.setEnableHardwareScaler(true)
            localVideoView.setMirror(false)

            // 设置渲染事件监听器
            localVideoView.setScalingType(RendererCommon.ScalingType.SCALE_ASPECT_FIT)
            localVideoView.setZOrderMediaOverlay(true)
        } catch (e: Exception) {
            Logger.e(TAG, "初始化视频渲染器失败", e)
            showError("初始化视频渲染器失败: ${e.message}")
        }
    }

    /**
     * 初始化WebRTC
     */
    private fun initWebRTC() {
        try {
            // 初始化日志
            val logDir = getExternalFilesDir("logs")
            if (logDir != null) {
                Logger.init(logDir, true)

                // 加载日志显示设置（确保在MainActivity中也能正确加载）
                val preferences = getSharedPreferences(Constants.PREF_NAME, Context.MODE_PRIVATE)
                val logDisplayEnabled = preferences.getBoolean(Constants.PREF_LOG_DISPLAY_ENABLED, Constants.DEFAULT_LOG_DISPLAY_ENABLED)
                Logger.setDisplayEnabled(logDisplayEnabled)
                Logger.i(TAG, "MainActivity加载日志显示设置: $logDisplayEnabled")
            }

            // 检查WebRTC管理器是否已初始化
            if (!WebRTCManager.isInitialized()) {
                // 初始化WebRTC管理器
                WebRTCManager.initialize(this, this)
                Logger.i(TAG, "WebRTC管理器已初始化")
            } else {
                Logger.i(TAG, "WebRTC管理器已经初始化，跳过")
            }

            // 不再自动连接信令服务器，等待用户点击启动服务
            Logger.i(TAG, "WebRTC初始化完成，等待用户启动服务")
        } catch (e: Exception) {
            Logger.e(TAG, "初始化WebRTC失败", e)
            showError("初始化WebRTC失败: ${e.message}")
        }
    }

    /**
     * 绑定服务
     */
    private fun bindService() {
        val intent = Intent(this, WebRTCSenderService::class.java)
        bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }

    /**
     * 解绑服务
     */
    private fun unbindService() {
        if (isBound) {
            unbindService(serviceConnection)
            isBound = false
        }
    }

    /**
     * 直接启动服务（不触发优化启动流程，避免循环）
     */
    private fun startServiceDirectly() {
        try {
            // 记录用户操作（包括首次安装的自动启动）
            recordManualOperation()

            // 先连接到信令服务器
            if (!WebRTCManager.isConnected()) {
                WebRTCManager.connectToSignalingServer()
                Logger.i(TAG, "正在连接到信令服务器...")
            }

            // 启动后台服务
            val intent = Intent(this, WebRTCSenderService::class.java).apply {
                action = WebRTCSenderService.ACTION_START_SERVICE
            }

            ContextCompat.startForegroundService(this, intent)
            Logger.i(TAG, "已启动WebRTC服务（直接启动）")

        } catch (e: Exception) {
            Logger.e(TAG, "直接启动服务失败", e)
            showError("启动服务失败: ${e.message}")
        }
    }

    /**
     * 启动服务
     */
    private fun startService() {
        try {
            Logger.d(TAG, "🔧 [调试] startService() 被调用")

            // 防止重复调用（1秒内只允许调用一次）
            val currentTime = System.currentTimeMillis()
            if (isServiceStarting || (currentTime - lastServiceStartTime) < 1000) {
                Logger.w(TAG, "🔧 [调试] startService() 调用过于频繁，跳过")
                return
            }

            isServiceStarting = true
            lastServiceStartTime = currentTime

            // 记录用户操作（包括首次安装的自动启动）
            recordManualOperation()

            // 先连接到信令服务器
            val isConnected = WebRTCManager.isConnected()
            Logger.d(TAG, "🔧 [调试] 当前连接状态: $isConnected")

            if (!isConnected) {
                WebRTCManager.connectToSignalingServer()
                Logger.i(TAG, "正在连接到信令服务器...")
            } else {
                Logger.d(TAG, "🔧 [调试] 信令服务器已连接，跳过连接步骤")
            }

            // 启动后台服务
            val intent = Intent(this, WebRTCSenderService::class.java).apply {
                action = WebRTCSenderService.ACTION_START_SERVICE
            }

            ContextCompat.startForegroundService(this, intent)
            Logger.i(TAG, "已启动WebRTC服务")

            // 不在信令连接时自动启动视频源
            // 视频源启动由优化启动流程控制
            Logger.i(TAG, "信令连接成功，等待优化启动流程控制视频源启动")

            // 只有在非优化启动流程中才触发优化启动
            if (!isOptimizedLaunchInProgress && !videoSourceStartupCompleted) {
                Logger.i(TAG, "🚀 [优化启动] 触发优化启动流程")
                startOptimizedLaunchSequence()
            } else if (videoSourceStartupCompleted) {
                Logger.i(TAG, "🚀 [优化启动] 视频源启动已完成，跳过重复启动")
            } else {
                Logger.i(TAG, "🚀 [优化启动] 优化启动流程进行中，跳过重复触发")
            }

        } catch (e: Exception) {
            Logger.e(TAG, "启动服务失败", e)
            showError("启动服务失败: ${e.message}")
        } finally {
            isServiceStarting = false
        }
    }

    /**
     * 停止服务（只停止推流，保持信令连接）
     */
    private fun stopService() {
        try {
            Logger.i(TAG, "停止推流服务（保持信令连接）")

            // 只停止视频源，不断开信令服务器连接
            WebRTCManager.stopVideoSource(this)

            Logger.i(TAG, "✅ 推流服务已停止，信令连接保持")
        } catch (e: Exception) {
            Logger.e(TAG, "停止推流服务失败", e)
            showError("停止推流服务失败: ${e.message}")
        }
    }

    /**
     * 完全停止服务（停止推流并断开信令连接）
     */
    private fun stopServiceCompletely() {
        try {
            Logger.i(TAG, "完全停止服务（断开信令连接）")

            // 先发送退出消息并断开信令服务器连接
            if (WebRTCManager.isConnected()) {
                Logger.i(TAG, "发送退出消息并断开信令服务器连接...")
                WebRTCManager.disconnectFromSignalingServerWithExitMessage()
            }

            // 停止后台服务
            val intent = Intent(this, WebRTCSenderService::class.java).apply {
                action = WebRTCSenderService.ACTION_STOP_SERVICE
            }

            startService(intent)
            Logger.i(TAG, "已完全停止WebRTC服务")
        } catch (e: Exception) {
            Logger.e(TAG, "完全停止服务失败", e)
            showError("完全停止服务失败: ${e.message}")
        }
    }

    /**
     * 记录用户操作（包括首次安装的自动启动）
     */
    private fun recordManualOperation() {
        val prefs = WebRTCManager.getPreferences()
        val isFirstInstall = prefs.getBoolean(Constants.PREF_FIRST_INSTALL, Constants.DEFAULT_FIRST_INSTALL)

        prefs.edit()
            .putBoolean(Constants.PREF_HAS_MANUAL_OPERATION, true)
            .putBoolean(Constants.PREF_FIRST_INSTALL, false) // 清除首次安装标志
            .apply()

        if (isFirstInstall) {
            Logger.i(TAG, "🎉 首次安装自动启动完成，已记录操作历史")
        } else {
            Logger.i(TAG, "已记录用户手动操作")
        }
    }

    /**
     * 记录用户视频源选择
     */
    private fun recordVideoSourceChoice(videoSource: String) {
        val prefs = WebRTCManager.getPreferences()
        prefs.edit()
            .putBoolean(Constants.PREF_HAS_MANUAL_OPERATION, true)
            .putString(Constants.PREF_VIDEO_SOURCE, videoSource)
            .apply()
        Logger.i(TAG, "已记录用户视频源选择: $videoSource")
    }

    /**
     * 为摄像头切换停止服务（不发送退出消息）
     */
    private fun stopServiceForCameraSwitch() {
        try {
            Logger.i(TAG, "为摄像头切换停止服务")

            // 只停止视频源，不停止整个服务和WebRTC管理器
            WebRTCManager.stopVideoSource(this)

            Logger.i(TAG, "已为摄像头切换停止视频源")
        } catch (e: Exception) {
            Logger.e(TAG, "为摄像头切换停止服务失败", e)
            showError("停止服务失败: ${e.message}")
        }
    }

    /**
     * 为摄像头切换启动服务
     */
    private fun startServiceForCameraSwitch() {
        try {
            Logger.i(TAG, "为摄像头切换启动服务")

            // 检查WebRTC管理器是否已初始化
            if (!WebRTCManager.isInitialized()) {
                Logger.i(TAG, "WebRTC管理器未初始化，重新初始化")
                WebRTCManager.initialize(this, this)
            }

            // 连接到信令服务器
            if (!WebRTCManager.isConnected()) {
                WebRTCManager.connectToSignalingServer()
                Logger.i(TAG, "正在连接到信令服务器...")
            }

            Logger.i(TAG, "摄像头切换服务准备完成")

            // 延迟启动摄像头视频源 - 等待摄像头硬件就绪
            Handler(mainLooper).postDelayed({
                try {
                    Logger.i(TAG, "3秒延迟后启动摄像头视频源")
                    WebRTCManager.startVideoSource(this@MainActivity)
                } catch (e: Exception) {
                    Logger.e(TAG, "启动摄像头视频源失败", e)
                    showError("启动摄像头失败: ${e.message}")
                }
            }, 3000) // 延迟3秒启动视频源

        } catch (e: Exception) {
            Logger.e(TAG, "为摄像头切换启动服务失败", e)
            showError("启动服务失败: ${e.message}")
        }
    }

    /**
     * 自动启动摄像头（不切换，直接启动）
     */
    private fun autoStartCamera() {
        try {
            Logger.i(TAG, "自动启动摄像头模式")

            // 停止当前视频源
            WebRTCManager.stopVideoSource(this)

            // 设置视频源类型为摄像头
            WebRTCManager.setVideoSourceType("camera")

            // 启动视频源
            WebRTCManager.startVideoSource(this)

            Logger.i(TAG, "摄像头已自动启动")
        } catch (e: Exception) {
            Logger.e(TAG, "自动启动摄像头失败", e)
            showError("自动启动摄像头失败: ${e.message}")
        }
    }

    /**
     * 直接启动摄像头（用于优化启动流程，不重启服务）
     */
    private fun startCameraCaptureDirectly() {
        try {
            Logger.i(TAG, "🚀 [优化启动] 直接启动摄像头，不重启服务")

            // 设置视频源类型为摄像头
            WebRTCManager.setVideoSourceType("camera")

            // 延迟1秒后启动摄像头
            Handler(mainLooper).postDelayed({
                Logger.i(TAG, "1秒延迟后启动摄像头")
                try {
                    WebRTCManager.startVideoSource(this@MainActivity)
                } catch (e: Exception) {
                    Logger.e(TAG, "直接启动摄像头失败", e)
                }
            }, 1000)

        } catch (e: Exception) {
            Logger.e(TAG, "直接启动摄像头失败", e)
        }
    }

    /**
     * 通过服务重启的方式安全切换摄像头
     */
    private fun startCameraCaptureWithServiceRestart() {
        try {
            // 检查是否在冷却期内
            val currentTime = System.currentTimeMillis()

            // 如果正在切换中，直接返回
            if (isCameraSwitching) {
                Logger.w(TAG, "摄像头正在切换中，请稍候")
                Toast.makeText(this, "摄像头正在切换中，请稍候", Toast.LENGTH_SHORT).show()
                return
            }

            // 检查冷却时间（只有在之前有过切换操作时才检查）
            if (lastCameraSwitchTime > 0) {
                val timeSinceLastSwitch = currentTime - lastCameraSwitchTime
                if (timeSinceLastSwitch < CAMERA_SWITCH_COOLDOWN) {
                    val remainingTime = (CAMERA_SWITCH_COOLDOWN - timeSinceLastSwitch) / 1000
                    Logger.w(TAG, "摄像头切换冷却中，剩余时间: ${remainingTime}秒")
                    Toast.makeText(this, "请等待 ${remainingTime} 秒后再切换摄像头", Toast.LENGTH_SHORT).show()
                    return
                }
            }

            // 设置切换标志
            isCameraSwitching = true
            lastCameraSwitchTime = currentTime

            Logger.i(TAG, "开始通过服务重启切换摄像头")

            // 记录用户摄像头选择
            recordVideoSourceChoice("camera")

            // 显示切换提示
            Toast.makeText(this, "正在切换摄像头，请稍候5秒...", Toast.LENGTH_LONG).show()

            // 检查当前视频源类型
            val currentSourceType = WebRTCManager.getVideoSourceType()

            if (currentSourceType == "camera") {
                // 如果当前已经是摄像头模式，则切换到下一个摄像头
                val availableCameras = WebRTCManager.getAvailableCameras(this)

                if (availableCameras.isEmpty()) {
                    Logger.w(TAG, "没有可用的摄像头，将在60秒后重试")
                    Toast.makeText(this, "没有可用的摄像头，将在60秒后重试", Toast.LENGTH_LONG).show()

                    // 重置切换标志，允许后续重试
                    isCameraSwitching = false
                    return
                }

                val currentCameraId = WebRTCManager.getCameraId()
                val currentIndex = availableCameras.indexOfFirst { it.id == currentCameraId }
                val nextIndex = if (currentIndex >= 0) {
                    (currentIndex + 1) % availableCameras.size
                } else {
                    0 // 如果当前摄像头不在列表中，使用第一个
                }

                val nextCamera = availableCameras[nextIndex]

                Logger.i(TAG, "🎥 [摄像头切换] 从 ${currentCameraId} 切换到 ${nextCamera.id} (${nextCamera.displayName})")

                // 先设置视频源类型为摄像头
                WebRTCManager.setVideoSourceType("camera")

                // 设置新的摄像头ID
                WebRTCManager.setCameraId(nextCamera.id)

                // 停止当前视频源
                stopServiceForCameraSwitch()

                // 延迟1秒后启动新摄像头
                Handler(mainLooper).postDelayed({
                    Logger.i(TAG, "1秒延迟后启动新摄像头")
                    try {
                        WebRTCManager.startVideoSource(this@MainActivity)
                        Toast.makeText(this, "已切换到: ${nextCamera.displayName}", Toast.LENGTH_SHORT).show()

                        // 摄像头切换成功，重置重试计数
                        cameraRetryCount = 0
                        Logger.d(TAG, "摄像头切换成功，重置重试计数")
                    } catch (e: Exception) {
                        Logger.e(TAG, "启动新摄像头失败", e)
                        showError("启动摄像头失败: ${e.message}")
                    }

                    // 重置切换标志
                    isCameraSwitching = false
                }, 1000) // 延迟1秒启动新摄像头

            } else {
                // 如果当前不是摄像头模式，则切换到摄像头模式
                Logger.i(TAG, "切换到摄像头模式")

                val availableCameras = WebRTCManager.getAvailableCameras(this)

                if (availableCameras.isEmpty()) {
                    Logger.w(TAG, "没有可用的摄像头，将在60秒后重试")
                    Toast.makeText(this, "没有可用的摄像头，将在60秒后重试", Toast.LENGTH_LONG).show()

                    // 重置切换标志，允许后续重试
                    isCameraSwitching = false
                    return
                }

                // 使用第一个可用摄像头
                val firstCamera = availableCameras[0]
                Logger.i(TAG, "🎥 [摄像头切换] 切换到摄像头模式，使用: ${firstCamera.displayName}")

                // 设置视频源类型为摄像头
                WebRTCManager.setVideoSourceType("camera")

                // 设置摄像头ID
                WebRTCManager.setCameraId(firstCamera.id)

                // 停止服务
                stopServiceForCameraSwitch()

                // 延迟1秒后启动摄像头
                Handler(mainLooper).postDelayed({
                    Logger.i(TAG, "1秒延迟后启动摄像头")
                    try {
                        WebRTCManager.startVideoSource(this@MainActivity)
                        Toast.makeText(this, "已切换到: ${firstCamera.displayName}", Toast.LENGTH_SHORT).show()

                        // 摄像头启动成功，重置重试计数
                        cameraRetryCount = 0
                        Logger.d(TAG, "摄像头启动成功，重置重试计数")
                    } catch (e: Exception) {
                        Logger.e(TAG, "启动摄像头失败", e)
                        showError("启动摄像头失败: ${e.message}")
                    }

                    // 重置切换标志
                    isCameraSwitching = false
                }, 1000) // 延迟1秒启动摄像头
            }

        } catch (e: Exception) {
            Logger.e(TAG, "通过服务重启切换摄像头失败", e)
            showError("切换摄像头失败: ${e.message}")

            // 异常情况下重置切换标志
            isCameraSwitching = false
        }
    }

    /**
     * 启动摄像头捕获或切换摄像头（旧方法，保留备用）
     */
    private fun startCameraCapture() {
        try {
            // 检查当前视频源类型
            val currentSourceType = WebRTCManager.getVideoSourceType()

            if (currentSourceType == "camera") {
                // 如果当前已经是摄像头模式，则切换摄像头
                val currentCameraId = WebRTCManager.getCameraId()
                val newCameraId = if (currentCameraId == "0") "1" else "0"

                Logger.i(TAG, "切换摄像头: $currentCameraId -> $newCameraId")

                // 停止当前视频源
                WebRTCManager.stopVideoSource(this)

                // 延迟启动新摄像头，确保前一个摄像头完全释放
                Handler(mainLooper).postDelayed({
                    try {
                        // 设置新的摄像头ID
                        WebRTCManager.setCameraId(newCameraId)

                        // 重新启动视频源
                        WebRTCManager.startVideoSource(this)

                        Toast.makeText(this, "已切换到" + (if (newCameraId == "0") "后置" else "前置") + "摄像头", Toast.LENGTH_SHORT).show()
                    } catch (e: Exception) {
                        Logger.e(TAG, "切换摄像头失败", e)
                        showError("切换摄像头失败: ${e.message}")
                    }
                }, 1000) // 延迟1秒确保摄像头资源完全释放
            } else {
                // 如果当前不是摄像头模式，则切换到摄像头模式
                Logger.i(TAG, "切换到摄像头模式")

                // 停止当前视频源
                WebRTCManager.stopVideoSource(this)

                // 延迟启动摄像头，确保前一个视频源完全释放
                Handler(mainLooper).postDelayed({
                    try {
                        // 设置视频源类型
                        WebRTCManager.setVideoSourceType("camera")

                        // 启动视频源
                        WebRTCManager.startVideoSource(this)

                        Toast.makeText(this, "已切换到摄像头模式", Toast.LENGTH_SHORT).show()
                    } catch (e: Exception) {
                        Logger.e(TAG, "切换到摄像头模式失败", e)
                        showError("切换到摄像头模式失败: ${e.message}")
                    }
                }, 1000) // 延迟1秒确保视频源完全释放
            }
        } catch (e: Exception) {
            Logger.e(TAG, "启动摄像头捕获失败", e)
            showError("启动摄像头捕获失败: ${e.message}")
        }
    }

    /**
     * 请求屏幕捕获权限
     */
    private fun requestScreenCapture() {
        try {
            Logger.i(TAG, "开始切换到屏幕录制模式")

            // 记录用户屏幕录制选择
            recordVideoSourceChoice("screen")

            // 检查当前视频源类型
            val currentSourceType = WebRTCManager.getVideoSourceType()
            Logger.i(TAG, "当前视频源类型: $currentSourceType")

            if (currentSourceType == "camera") {
                // 如果当前是摄像头模式，需要先停止摄像头并释放资源
                Logger.i(TAG, "🎥 [视频源切换] 从摄像头模式切换到屏幕录制，先停止摄像头")

                // 显示切换提示
                Toast.makeText(this, "正在从摄像头切换到屏幕录制...", Toast.LENGTH_SHORT).show()

                // 停止服务以释放摄像头资源
                stopServiceForCameraSwitch()

                // 延迟一段时间确保摄像头完全释放，然后请求屏幕捕获权限
                Handler(mainLooper).postDelayed({
                    Logger.i(TAG, "🎥 [视频源切换] 摄像头已停止，开始请求屏幕捕获权限")

                    // 设置视频源类型
                    WebRTCManager.setVideoSourceType("screen")

                    // 请求屏幕捕获权限
                    val captureIntent = mediaProjectionManager.createScreenCaptureIntent()
                    startActivityForResult(captureIntent, SCREEN_CAPTURE_REQUEST_CODE)
                }, 1500) // 延迟1.5秒确保摄像头完全释放

            } else {
                // 如果当前不是摄像头模式，直接切换到屏幕录制
                Logger.i(TAG, "🎥 [视频源切换] 直接切换到屏幕录制模式")

                // 设置视频源类型
                WebRTCManager.setVideoSourceType("screen")

                // 请求屏幕捕获权限
                val captureIntent = mediaProjectionManager.createScreenCaptureIntent()
                startActivityForResult(captureIntent, SCREEN_CAPTURE_REQUEST_CODE)
            }

        } catch (e: Exception) {
            Logger.e(TAG, "请求屏幕捕获权限失败", e)
            showError("请求屏幕捕获权限失败: ${e.message}")
        }
    }

    /**
     * 处理屏幕捕获结果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == SCREEN_CAPTURE_REQUEST_CODE) {
            if (resultCode == Activity.RESULT_OK && data != null) {
                // 创建媒体投影
                val mediaProjection = mediaProjectionManager.getMediaProjection(resultCode, data)

                // 设置媒体投影
                WebRTCManager.setMediaProjection(mediaProjection)

                // 设置媒体投影数据
                WebRTCManager.setMediaProjectionData(data)

                // 启动屏幕捕获
                startScreenCapture()
            } else {
                showError("用户取消了屏幕捕获")
            }
        }
    }

    /**
     * 检查权限
     */
    private fun checkPermissions(): Boolean {
        for (permission in requiredPermissions) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false
            }
        }

        // 简化权限检查，只检查基本权限
        return true
    }

    /**
     * 请求权限
     */
    private fun requestPermissions() {
        ActivityCompat.requestPermissions(this, requiredPermissions, PERMISSIONS_REQUEST_CODE)
    }

    /**
     * 处理权限请求结果
     */
    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        if (requestCode == PERMISSIONS_REQUEST_CODE) {
            var allGranted = true

            for (result in grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allGranted = false
                    break
                }
            }

            if (allGranted) {
                // 初始化WebRTC
                initWebRTC()

                // 检查是否应该自动启动（首次安装或基于用户历史操作）
                if (shouldAutoStartService()) {
                    val prefs = WebRTCManager.getPreferences()
                    val isFirstInstall = prefs.getBoolean(Constants.PREF_FIRST_INSTALL, Constants.DEFAULT_FIRST_INSTALL)
                    val hasManualOperation = prefs.getBoolean(Constants.PREF_HAS_MANUAL_OPERATION, false)

                    if (isFirstInstall) {
                        Logger.i(TAG, "🎉 权限获取后检测到首次安装，自动启动服务和摄像头")
                    } else if (hasManualOperation) {
                        Logger.i(TAG, "权限获取后检测到用户历史操作，开始优化启动流程")
                    }

                    startOptimizedLaunchSequence()
                } else {
                    Logger.i(TAG, "权限获取后无需自动启动")
                }
            } else {
                // 显示权限说明
                showPermissionExplanation()
            }
        }
    }



    /**
     * 显示屏幕录制恢复对话框
     */
    private fun showScreenCaptureRecoveryDialog(reason: String) {
        AlertDialog.Builder(this)
            .setTitle("屏幕录制中断")
            .setMessage("屏幕录制因外部应用（如scrcpy）关闭而中断。\n\n原因: $reason\n\n是否重新启动屏幕录制？")
            .setPositiveButton("重新启动") { _, _ ->
                Logger.i(TAG, "用户选择重新启动屏幕录制")
                requestScreenCapture()
            }
            .setNegativeButton("切换到摄像头") { _, _ ->
                Logger.i(TAG, "用户选择切换到摄像头模式")
                startCameraCaptureWithServiceRestart()
            }
            .setNeutralButton("停止推流") { _, _ ->
                Logger.i(TAG, "用户选择停止推流")
                stopService()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 显示权限说明
     */
    private fun showPermissionExplanation() {
        AlertDialog.Builder(this)
            .setTitle("需要权限")
            .setMessage("此应用需要摄像头和麦克风权限才能正常工作。请在设置中授予这些权限。")
            .setPositiveButton("设置") { _, _ ->
                // 打开应用设置
                val intent = Intent(android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri = android.net.Uri.fromParts("package", packageName, null)
                intent.data = uri
                startActivity(intent)
            }
            .setNegativeButton("取消") { _, _ ->
                // 显示功能受限提示
                Toast.makeText(this, "功能受限：无法使用摄像头和麦克风", Toast.LENGTH_LONG).show()
            }
            .setCancelable(false)
            .show()
    }

    /**
     * 启动屏幕捕获
     */
    private fun startScreenCapture() {
        Logger.i(TAG, "🎥 [屏幕录制] 启动屏幕捕获服务")

        // 确保视频源类型被正确设置为屏幕录制
        WebRTCManager.setVideoSourceType("screen")
        Logger.i(TAG, "🎥 [屏幕录制] 视频源类型已设置为: screen")

        val intent = Intent(this, WebRTCSenderService::class.java).apply {
            action = WebRTCSenderService.ACTION_START_SCREEN_CAPTURE
        }

        startService(intent)
    }

    /**
     * 更新连接状态显示
     */
    private fun updateConnectionStatus() {
        val isConnected = WebRTCManager.isConnected()
        val isHealthy = WebRTCManager.isConnectionHealthy()

        runOnUiThread {
            connectionText.text = when {
                isConnected && isHealthy -> "已连接到服务器"
                isConnected && !isHealthy -> "连接异常，正在检查..."
                else -> "未连接到服务器"
            }
        }

        Logger.d(TAG, "🔍 更新连接状态: connected=$isConnected, healthy=$isHealthy")

        // 处理不同的连接状态 - 添加冷却时间避免频繁重连
        val currentTime = System.currentTimeMillis()
        val lastReconnectTime = getSharedPreferences("app_state", MODE_PRIVATE)
            .getLong("last_reconnect_time", 0)

        when {
            isConnected && !isHealthy -> {
                // 连接异常时，添加30秒冷却时间
                if (currentTime - lastReconnectTime > 30000) {
                    Logger.w(TAG, "🔍 检测到连接异常，尝试强制重连")
                    getSharedPreferences("app_state", MODE_PRIVATE)
                        .edit()
                        .putLong("last_reconnect_time", currentTime)
                        .apply()
                    WebRTCManager.forceReconnect()
                } else {
                    Logger.d(TAG, "🔍 连接异常但在冷却期内，跳过重连")
                }
            }
            !isConnected -> {
                // 未连接时，添加15秒冷却时间
                if (currentTime - lastReconnectTime > 15000) {
                    Logger.w(TAG, "🔍 检测到未连接状态，检查是否需要重连")
                    // 检查服务是否在运行，如果在运行但未连接，尝试重连
                    service?.let { service ->
                        if (service.isRunning()) {
                            // 检查SignalingClient是否已经在重连中
                            if (!WebRTCManager.isReconnecting()) {
                                Logger.i(TAG, "🔍 服务正在运行但信令服务器未连接，尝试重连")
                                getSharedPreferences("app_state", MODE_PRIVATE)
                                    .edit()
                                    .putLong("last_reconnect_time", currentTime)
                                    .apply()
                                WebRTCManager.connectToSignalingServer()
                            } else {
                                Logger.d(TAG, "🔍 SignalingClient已在重连中，跳过重复重连")
                            }
                        }
                    }
                } else {
                    Logger.d(TAG, "🔍 未连接但在冷却期内，跳过重连")
                }
            }
        }
    }

    /**
     * 启动定期状态更新
     */
    private fun startPeriodicStatusUpdate() {
        val handler = Handler(mainLooper)
        val updateRunnable = object : Runnable {
            override fun run() {
                updateConnectionStatus()
                updateVideoSourceStatus()
                handler.postDelayed(this, 60000) // 每60秒更新一次
            }
        }
        handler.post(updateRunnable)
    }

    /**
     * 更新视频源状态显示
     */
    private fun updateVideoSourceStatus() {
        val videoSourceType = WebRTCManager.getVideoSourceType()
        val statusText = when (videoSourceType) {
            "camera" -> "视频源: 摄像头"
            "screen" -> "视频源: 屏幕录制"
            "hdmiin" -> "视频源: HDMI输入"
            "usbcapture" -> "视频源: USB采集卡"
            else -> "视频源: 未启动"
        }
        videoSourceText.text = statusText
        Logger.d(TAG, "更新视频源状态: $videoSourceType")

        // 只有在视频源真正未启动时才尝试智能重试
        if (videoSourceType == "未启动") {
            val preferredSource = WebRTCManager.getConfiguredVideoSourceType()

            when (preferredSource) {
                "camera" -> {
                    Logger.d(TAG, "🎥 [状态更新] 视频源未启动且用户偏好摄像头，触发重试检查")
                    checkAndRetryCameraIfNeeded()
                }
                "hdmiin" -> {
                    Logger.d(TAG, "🎥 [状态更新] 视频源未启动且用户偏好HDMI输入，触发重试检查")
                    checkAndRetryHdmiIfNeeded()
                }
                "usbcapture" -> {
                    Logger.d(TAG, "🎥 [状态更新] 视频源未启动且用户偏好USB采集卡，触发重试检查")
                    checkAndRetryUsbCaptureIfNeeded()
                }
                else -> {
                    Logger.d(TAG, "🎥 [状态更新] 视频源未启动但用户偏好非自动重试类型，跳过重试")
                }
            }
        }
    }



    /**
     * 启动HDMI输入捕获
     */
    private fun startHdmiCapture() {
        try {
            Logger.i(TAG, "开始切换到HDMI输入模式")

            // 记录用户HDMI输入选择
            recordVideoSourceChoice("hdmiin")

            // 检查当前视频源类型
            val currentSourceType = WebRTCManager.getVideoSourceType()
            Logger.i(TAG, "当前视频源类型: $currentSourceType")

            if (currentSourceType != "hdmiin") {
                // 如果当前不是HDMI输入模式，需要先停止当前视频源
                Logger.i(TAG, "🎥 [视频源切换] 从 $currentSourceType 切换到HDMI输入")

                // 显示切换提示
                Toast.makeText(this, "正在切换到HDMI输入...", Toast.LENGTH_SHORT).show()

                // 停止服务以释放当前视频源
                stopServiceForVideoSourceSwitch()

                // 延迟一段时间确保资源完全释放，然后启动HDMI输入
                Handler(mainLooper).postDelayed({
                    Logger.i(TAG, "🎥 [视频源切换] 当前视频源已停止，开始启动HDMI输入")

                    // 设置视频源类型
                    WebRTCManager.setVideoSourceType("hdmiin")

                    // 启动HDMI输入
                    try {
                        WebRTCManager.startVideoSource(this@MainActivity)
                        Toast.makeText(this, "已切换到HDMI输入", Toast.LENGTH_SHORT).show()
                    } catch (e: Exception) {
                        Logger.e(TAG, "启动HDMI输入失败", e)
                        showError("启动HDMI输入失败: ${e.message}")
                    }
                }, 1500) // 延迟1.5秒确保资源完全释放
            } else {
                // 如果当前已经是HDMI输入模式，显示提示
                Toast.makeText(this, "当前已是HDMI输入模式", Toast.LENGTH_SHORT).show()
            }

        } catch (e: Exception) {
            Logger.e(TAG, "启动HDMI输入捕获失败", e)
            showError("启动HDMI输入捕获失败: ${e.message}")
        }
    }

    /**
     * 启动USB采集卡捕获
     */
    private fun startUsbCapture() {
        try {
            Logger.i(TAG, "开始切换到USB采集卡模式")

            // 记录用户USB采集卡选择
            recordVideoSourceChoice("usbcapture")

            // 检查当前视频源类型
            val currentSourceType = WebRTCManager.getVideoSourceType()
            Logger.i(TAG, "当前视频源类型: $currentSourceType")

            if (currentSourceType != "usbcapture") {
                // 如果当前不是USB采集卡模式，需要先停止当前视频源
                Logger.i(TAG, "🎥 [视频源切换] 从 $currentSourceType 切换到USB采集卡")

                // 显示切换提示
                Toast.makeText(this, "正在切换到USB采集卡...", Toast.LENGTH_SHORT).show()

                // 停止服务以释放当前视频源
                stopServiceForVideoSourceSwitch()

                // 延迟一段时间确保资源完全释放，然后启动USB采集卡
                Handler(mainLooper).postDelayed({
                    Logger.i(TAG, "🎥 [视频源切换] 当前视频源已停止，开始启动USB采集卡")

                    // 设置视频源类型
                    WebRTCManager.setVideoSourceType("usbcapture")

                    // 启动USB采集卡
                    try {
                        WebRTCManager.startVideoSource(this@MainActivity)
                        Toast.makeText(this, "已切换到USB采集卡", Toast.LENGTH_SHORT).show()
                    } catch (e: Exception) {
                        Logger.e(TAG, "启动USB采集卡失败", e)
                        showError("启动USB采集卡失败: ${e.message}")
                    }
                }, 1500) // 延迟1.5秒确保资源完全释放
            } else {
                // 如果当前已经是USB采集卡模式，显示提示
                Toast.makeText(this, "当前已是USB采集卡模式", Toast.LENGTH_SHORT).show()
            }

        } catch (e: Exception) {
            Logger.e(TAG, "启动USB采集卡捕获失败", e)
            showError("启动USB采集卡捕获失败: ${e.message}")
        }
    }

    /**
     * 为视频源切换停止服务（不发送退出消息）
     */
    private fun stopServiceForVideoSourceSwitch() {
        try {
            Logger.i(TAG, "为视频源切换停止服务")

            // 只停止视频源，不停止整个服务和WebRTC管理器
            WebRTCManager.stopVideoSource(this)

            Logger.i(TAG, "已为视频源切换停止视频源")
        } catch (e: Exception) {
            Logger.e(TAG, "为视频源切换停止服务失败", e)
            showError("停止服务失败: ${e.message}")
        }
    }

    /**
     * 检查并智能重试摄像头
     */
    private fun checkAndRetryCameraIfNeeded() {
        val preferredSource = WebRTCManager.getConfiguredVideoSourceType()

        // 只有当用户偏好是摄像头时才重试
        if (preferredSource != "camera") {
            return
        }

        val currentTime = System.currentTimeMillis()

        // 检查是否在禁用期内（避免与优化启动流程冲突）
        if (currentTime < disableCameraRetryUntil) {
            Logger.d(TAG, "🎥 [摄像头重试] 重试机制暂时禁用中，避免与启动流程冲突")
            return
        }

        // 检查是否在重试冷却期内（60秒）
        if (currentTime - lastCameraRetryTime < 60000) {
            return
        }

        // 检查重试次数
        if (cameraRetryCount >= MAX_CAMERA_RETRY) {
            Logger.w(TAG, "摄像头重试次数已达上限，停止重试")
            return
        }

        // 首先检查当前视频源状态
        val currentVideoSource = WebRTCManager.getVideoSourceType()
        val videoStatus = WebRTCManager.checkVideoStreamStatus()
        val isVideoSourceActive = WebRTCManager.isVideoSourceActive()

        Logger.i(TAG, "🎥 [摄像头重试] 当前状态 - 视频源: $currentVideoSource, 状态: $videoStatus, 活跃: $isVideoSourceActive")

        // 🔧 修复：如果摄像头模式且视频源活跃，说明摄像头正常工作，跳过重试
        if (currentVideoSource == "camera" && isVideoSourceActive) {
            Logger.d(TAG, "🎥 [摄像头重试] ✅ 摄像头模式且视频源活跃，摄像头正常工作，跳过重试")
            // 重置重试计数，因为摄像头工作正常
            if (cameraRetryCount > 0) {
                Logger.i(TAG, "🎥 [摄像头重试] 摄像头恢复正常，重置重试计数")
                cameraRetryCount = 0
            }
            return
        }

        // 如果摄像头模式且状态正常，也跳过重试
        if (currentVideoSource == "camera" && videoStatus.contains("正常运行")) {
            Logger.d(TAG, "🎥 [摄像头重试] 摄像头状态正常，跳过重试")
            return
        }

        // 🔧 修复：只有在摄像头模式但视频源未激活，或者状态明确异常时才重试
        val shouldRetry = when {
            currentVideoSource != "camera" -> {
                Logger.i(TAG, "🎥 [摄像头重试] 当前非摄像头模式，需要启动摄像头模式")
                true
            }
            currentVideoSource == "camera" && !isVideoSourceActive -> {
                Logger.w(TAG, "🎥 [摄像头重试] 摄像头模式但视频源未激活，需要重试")
                true
            }
            currentVideoSource == "camera" && (videoStatus.contains("未激活") || videoStatus.contains("异常") || videoStatus.contains("失败")) -> {
                Logger.w(TAG, "🎥 [摄像头重试] 检测到摄像头模式但状态异常: $videoStatus")
                true
            }
            else -> {
                Logger.d(TAG, "🎥 [摄像头重试] 状态正常或不需要重试: $videoStatus")
                false
            }
        }

        if (!shouldRetry) {
            return
        }

        // 检查摄像头是否可用
        val availableCameras = WebRTCManager.getAvailableCameras(this)
        if (availableCameras.isNotEmpty()) {
            Logger.i(TAG, "🎥 [摄像头重试] 检测到可用摄像头，尝试启动摄像头模式")
            cameraRetryCount++
            lastCameraRetryTime = currentTime

            // 尝试启动摄像头
            startCameraCaptureWithServiceRestart()
        } else {
            Logger.d(TAG, "🎥 [摄像头重试] 摄像头仍不可用，等待下次检查")
        }
    }

    /**
     * 检查并智能重试HDMI输入
     */
    private fun checkAndRetryHdmiIfNeeded() {
        val preferredSource = WebRTCManager.getConfiguredVideoSourceType()

        // 只有当用户偏好是HDMI输入时才重试
        if (preferredSource != "hdmiin") {
            return
        }

        val currentTime = System.currentTimeMillis()

        // 检查是否在重试冷却期内（60秒）
        if (currentTime - lastCameraRetryTime < 60000) {
            return
        }

        // 检查重试次数
        if (cameraRetryCount >= MAX_CAMERA_RETRY) {
            Logger.w(TAG, "HDMI输入重试次数已达上限，停止重试")
            return
        }

        // 首先检查当前视频源状态
        val currentVideoSource = WebRTCManager.getVideoSourceType()
        val videoStatus = WebRTCManager.checkVideoStreamStatus()

        Logger.i(TAG, "🎥 [HDMI重试] 当前状态 - 视频源: $currentVideoSource, 状态: $videoStatus")

        // 如果HDMI输入模式且状态正常，跳过重试
        if (currentVideoSource == "hdmiin" && videoStatus.contains("正常运行")) {
            Logger.d(TAG, "🎥 [HDMI重试] HDMI输入状态正常，跳过重试")
            return
        }

        // 如果HDMI输入模式但状态异常，或者当前非HDMI输入模式
        if (currentVideoSource != "hdmiin" || videoStatus.contains("未激活") || videoStatus.contains("异常") || videoStatus.contains("失败")) {
            Logger.i(TAG, "🎥 [HDMI重试] 检测到需要启动HDMI输入模式")
            cameraRetryCount++
            lastCameraRetryTime = currentTime

            // 尝试启动HDMI输入
            startHdmiCapture()
        }
    }

    /**
     * 检查并智能重试USB采集卡
     */
    private fun checkAndRetryUsbCaptureIfNeeded() {
        val preferredSource = WebRTCManager.getConfiguredVideoSourceType()

        // 只有当用户偏好是USB采集卡时才重试
        if (preferredSource != "usbcapture") {
            return
        }

        val currentTime = System.currentTimeMillis()

        // 检查是否在重试冷却期内（60秒）
        if (currentTime - lastCameraRetryTime < 60000) {
            return
        }

        // 检查重试次数
        if (cameraRetryCount >= MAX_CAMERA_RETRY) {
            Logger.w(TAG, "USB采集卡重试次数已达上限，停止重试")
            return
        }

        // 首先检查当前视频源状态
        val currentVideoSource = WebRTCManager.getVideoSourceType()
        val videoStatus = WebRTCManager.checkVideoStreamStatus()

        Logger.i(TAG, "🎥 [USB采集卡重试] 当前状态 - 视频源: $currentVideoSource, 状态: $videoStatus")

        // 如果USB采集卡模式且状态正常，跳过重试
        if (currentVideoSource == "usbcapture" && videoStatus.contains("正常运行")) {
            Logger.d(TAG, "🎥 [USB采集卡重试] USB采集卡状态正常，跳过重试")
            return
        }

        // 如果USB采集卡模式但状态异常，或者当前非USB采集卡模式
        if (currentVideoSource != "usbcapture" || videoStatus.contains("未激活") || videoStatus.contains("异常") || videoStatus.contains("失败")) {
            Logger.i(TAG, "🎥 [USB采集卡重试] 检测到需要启动USB采集卡模式")
            cameraRetryCount++
            lastCameraRetryTime = currentTime

            // 尝试启动USB采集卡
            startUsbCapture()
        }
    }

    /**
     * 启动摄像头定期检查机制
     */
    private fun startCameraRetryTimer() {
        // 停止之前的定时器
        stopCameraRetryTimer()

        val prefs = WebRTCManager.getPreferences()
        val preferredSource = prefs.getString(Constants.PREF_VIDEO_SOURCE, "camera")

        // 只有当用户偏好是摄像头时才启动定时器
        if (preferredSource != "camera") {
            Logger.d(TAG, "用户偏好不是摄像头，不启动摄像头重试定时器")
            return
        }

        Logger.i(TAG, "🎥 [摄像头重试] 启动定期检查机制，每60秒检查一次")

        cameraRetryHandler = Handler(Looper.getMainLooper())
        cameraRetryRunnable = object : Runnable {
            override fun run() {
                try {
                    Logger.d(TAG, "🎥 [摄像头重试] 执行定期检查")
                    checkAndRetryCameraIfNeeded()

                    // 继续下一次检查
                    cameraRetryHandler?.postDelayed(this, 60000) // 60秒后再次检查
                } catch (e: Exception) {
                    Logger.e(TAG, "摄像头重试检查失败", e)
                    // 即使出错也要继续检查
                    cameraRetryHandler?.postDelayed(this, 60000)
                }
            }
        }

        // 立即执行第一次检查，然后每60秒检查一次
        cameraRetryHandler?.post(cameraRetryRunnable!!)
    }

    /**
     * 停止摄像头定期检查机制
     */
    private fun stopCameraRetryTimer() {
        cameraRetryRunnable?.let { runnable ->
            cameraRetryHandler?.removeCallbacks(runnable)
        }
        cameraRetryHandler = null
        cameraRetryRunnable = null
        Logger.d(TAG, "🎥 [摄像头重试] 停止定期检查机制")
    }

    /**
     * 更新UI
     */
    private fun updateUI() {
        service?.let { service ->
            if (service.isRunning()) {
                startButton.isEnabled = false
                stopButton.isEnabled = true
                cameraButton.isEnabled = true
                screenButton.isEnabled = true

                statusText.text = if (service.isCapturing()) "正在推流" else "服务已启动"
            } else {
                startButton.isEnabled = true
                stopButton.isEnabled = false
                cameraButton.isEnabled = false
                screenButton.isEnabled = false

                statusText.text = "服务未启动"
            }
        }

        // 同时更新连接状态和视频源状态
        updateConnectionStatus()
        updateVideoSourceStatus()
    }

    /**
     * 显示错误
     */
    private fun showError(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_LONG).show()
    }

    /**
     * 检查网络是否可用
     */
    private fun isNetworkAvailable(): Boolean {
        return try {
            val connectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val network = connectivityManager.activeNetwork ?: return false
                val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
                capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET) &&
                capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_VALIDATED)
            } else {
                @Suppress("DEPRECATION")
                val networkInfo = connectivityManager.activeNetworkInfo
                networkInfo?.isConnected == true
            }
        } catch (e: Exception) {
            Logger.e(TAG, "检查网络状态失败", e)
            false
        }
    }

    /**
     * 获取本地视频轨道
     */
    private fun getLocalVideoTrack(): VideoTrack? {
        try {
            // 从WebRTCManager获取WebRTCClient
            val client = WebRTCManager.webRTCClient
            Logger.d(TAG, "获取WebRTCClient: ${client != null}")

            if (client != null) {
                // 使用公开方法获取本地视频轨道
                val videoTrack = client.getLocalVideoTrack()
                Logger.d(TAG, "获取本地视频轨道: ${videoTrack != null}")
                return videoTrack
            } else {
                Logger.w(TAG, "WebRTCClient为null，无法获取视频轨道")
            }
        } catch (e: Exception) {
            Logger.e(TAG, "获取本地视频轨道失败", e)
        }
        return null
    }





    // WebRTCManager.WebRTCManagerListener实现

    override fun onWebRTCInitialized() {
        Logger.i(TAG, "推流服务器已初始化")
    }

    override fun onSignalingConnected() {
        Logger.i(TAG, "已连接到推流服务器")

        runOnUiThread {
            connectionText.text = "已连接到推流服务器"
        }
    }

    override fun onSignalingDisconnected() {
        Logger.i(TAG, "已断开与推流服务器的连接")

        runOnUiThread {
            connectionText.text = "未连接到推流服务器"
        }
    }

    override fun onLocalVideoTrackAvailable(videoTrack: VideoTrack?) {
        Logger.i(TAG, "本地视频轨道可用: ${videoTrack != null}")

        runOnUiThread {
            if (videoTrack != null) {
                localVideoView.visibility = View.VISIBLE
                videoTrack.addSink(localVideoView)
            } else {
                localVideoView.visibility = View.GONE
            }
        }
    }

    override fun onVideoSourceStarted(sourceType: String) {
        Logger.i(TAG, "视频源已启动: $sourceType")

        // 标记视频源启动完成，防止重复启动
        videoSourceStartupCompleted = true

        // 🔧 修复：摄像头启动成功后，停止重试定时器避免重复启动
        if (sourceType == "camera") {
            Logger.i(TAG, "🎥 [摄像头启动成功] 停止摄像头重试定时器，避免重复启动")
            stopCameraRetryTimer()
            // 重置重试计数
            cameraRetryCount = 0
        }

        runOnUiThread {
            statusText.text = "正在推流 ($sourceType)"

            // 更新按钮状态：推流启动后，开始按钮不可用，停止按钮可用
            startButton.isEnabled = false
            stopButton.isEnabled = true
            cameraButton.isEnabled = true
            screenButton.isEnabled = true

            updateVideoSourceStatus()

            Logger.d(TAG, "UI状态已更新：推流已启动 ($sourceType)")
        }
    }

    override fun onVideoSourceStopped() {
        Logger.i(TAG, "视频源已停止")

        // 重置视频源启动标志，允许重新启动
        videoSourceStartupCompleted = false

        runOnUiThread {
            statusText.text = "已停止推流"

            // 更新按钮状态：推流停止后，开始按钮可用，停止按钮不可用
            startButton.isEnabled = true
            stopButton.isEnabled = false
            // 摄像头和屏幕按钮保持可用，允许切换视频源
            cameraButton.isEnabled = true
            screenButton.isEnabled = true

            updateVideoSourceStatus()

            Logger.d(TAG, "UI状态已更新：推流已停止")
        }
    }

    override fun onVideoSourcePaused() {
        Logger.i(TAG, "视频源已暂停")

        runOnUiThread {
            statusText.text = "已暂停推流"
        }
    }

    override fun onVideoSourceResumed() {
        Logger.i(TAG, "视频源已恢复")

        runOnUiThread {
            statusText.text = "正在推流"
        }
    }

    override fun onViewerJoined(viewerId: String) {
        Logger.i(TAG, "观众加入: $viewerId")
    }

    override fun onViewerLeft(viewerId: String) {
        Logger.i(TAG, "观众离开: $viewerId")
    }

    override fun onPeerConnected(peerId: String) {
        Logger.i(TAG, "对等端连接: $peerId")
    }

    override fun onPeerDisconnected(peerId: String) {
        Logger.i(TAG, "对等端断开连接: $peerId")
    }

    override fun onError(message: String) {
        Logger.e(TAG, "错误: $message")

        runOnUiThread {
            showError(message)
        }
    }

    /**
     * 检查视频流状态
     */
    private fun checkVideoStreamStatus() {
        try {
            Logger.i(TAG, "🎥 [手动检查] 用户手动检查视频流状态")

            // 显示检查中状态
            runOnUiThread {
                statusText.text = "正在检查视频流状态..."
            }

            // 在后台线程执行检查
            Thread {
                try {
                    val statusResult = WebRTCManager.checkVideoStreamStatus()

                    runOnUiThread {
                        // 显示检查结果
                        val dialog = AlertDialog.Builder(this)
                            .setTitle("视频流状态检查")
                            .setMessage("检查结果：\n$statusResult")
                            .setPositiveButton("确定") { _, _ -> }
                            .setNeutralButton("查看详细日志") { _, _ ->
                                // 可以在这里添加查看详细日志的功能
                                Toast.makeText(this, "请查看Logcat获取详细日志", Toast.LENGTH_LONG).show()
                            }
                            .create()

                        dialog.show()

                        // 恢复状态显示
                        updateUI()
                    }
                } catch (e: Exception) {
                    Logger.e(TAG, "检查视频流状态失败", e)
                    runOnUiThread {
                        showError("检查视频流状态失败: ${e.message}")
                        updateUI()
                    }
                }
            }.start()

        } catch (e: Exception) {
            Logger.e(TAG, "启动视频流状态检查失败", e)
            showError("启动检查失败: ${e.message}")
        }
    }

    /**
     * 打开设置页面
     */
    private fun openSettings() {
        try {
            val intent = Intent(this, SettingsActivity::class.java)
            startActivity(intent)
        } catch (e: Exception) {
            Logger.e(TAG, "打开设置页面失败", e)
            showError("打开设置页面失败: ${e.message}")
        }
    }

    /**
     * 设置ActionBar标题显示版本信息
     */
    private fun setupActionBarTitle() {
        try {
            // 获取应用版本信息
            val packageInfo = packageManager.getPackageInfo(packageName, 0)
            val versionName = packageInfo.versionName

            // 设置ActionBar标题
            supportActionBar?.title = "线上线下服务台 v$versionName"

            // Logger.i(TAG, "设置ActionBar标题: WebRTC发送端 v$versionName")
        } catch (e: Exception) {
            // Logger.e(TAG, "设置ActionBar标题失败: ${e.message}")
            // 如果获取版本失败，使用默认标题
            supportActionBar?.title = "线上线下服务台"
        }
    }

    /**
     * 清理遗留的APK文件
     */
    private fun cleanupLeftoverApkFiles() {
        try {
            Logger.d(TAG, "🧹 开始清理遗留的APK文件")

            // 获取待清理的APK文件路径
            val prefs = getSharedPreferences("upgrade_cleanup", Context.MODE_PRIVATE)
            val apkPaths = prefs.getStringSet("apk_paths", mutableSetOf()) ?: mutableSetOf()

            if (apkPaths.isNotEmpty()) {
                Logger.i(TAG, "🗑️ 发现 ${apkPaths.size} 个待清理的APK文件")

                val cleanedPaths = mutableSetOf<String>()

                for (apkPath in apkPaths) {
                    try {
                        val apkFile = File(apkPath)
                        if (apkFile.exists()) {
                            if (apkFile.delete()) {
                                Logger.i(TAG, "✅ 清理APK文件成功: ${apkFile.name}")
                                cleanedPaths.add(apkPath)
                            } else {
                                Logger.w(TAG, "⚠️ 清理APK文件失败: ${apkFile.name}")
                            }
                        } else {
                            Logger.d(TAG, "📁 APK文件已不存在: ${apkFile.name}")
                            cleanedPaths.add(apkPath)
                        }
                    } catch (e: Exception) {
                        Logger.e(TAG, "❌ 清理APK文件异常: $apkPath - ${e.message}")
                    }
                }

                // 移除已清理的路径
                if (cleanedPaths.isNotEmpty()) {
                    val remainingPaths = apkPaths - cleanedPaths
                    prefs.edit().putStringSet("apk_paths", remainingPaths).apply()
                    Logger.i(TAG, "🧹 已清理 ${cleanedPaths.size} 个APK文件，剩余 ${remainingPaths.size} 个")
                }
            } else {
                Logger.d(TAG, "📁 无待清理的APK文件")
            }

            // 额外清理Downloads目录中的版本APK文件
            cleanupDownloadsApkFiles()

        } catch (e: Exception) {
            Logger.e(TAG, "❌ 清理遗留APK文件异常: ${e.message}")
        }
    }

    /**
     * 清理Downloads目录中的版本APK文件
     */
    private fun cleanupDownloadsApkFiles() {
        try {
            val downloadDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
            if (!downloadDir.exists()) return

            val apkFiles = downloadDir.listFiles { file ->
                file.name.matches(Regex("update_v.*\\.apk"))
            }

            if (apkFiles != null && apkFiles.isNotEmpty()) {
                Logger.i(TAG, "🔍 在Downloads目录发现 ${apkFiles.size} 个版本APK文件")

                for (apkFile in apkFiles) {
                    try {
                        if (apkFile.delete()) {
                            Logger.i(TAG, "✅ 清理Downloads APK文件成功: ${apkFile.name}")
                        } else {
                            Logger.w(TAG, "⚠️ 清理Downloads APK文件失败: ${apkFile.name}")
                        }
                    } catch (e: Exception) {
                        Logger.e(TAG, "❌ 清理Downloads APK文件异常: ${apkFile.name} - ${e.message}")
                    }
                }
            }
        } catch (e: Exception) {
            Logger.e(TAG, "❌ 清理Downloads APK文件异常: ${e.message}")
        }
    }
}
