/ Header Record For PersistentHashMapValueStorageL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\WebRTCSenderApp.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\WebRTCSenderApp.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\audio\AudioSourceTester.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\audio\AudioSourceTester.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\audio\AudioSourceTester.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\audio\AudioSourceTester.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\audio\CaptureCardAudioManager.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\audio\CaptureCardAudioManager.ktV U$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\CommandDispatcher.ktV U$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\CommandDispatcher.ktS R$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\CommandHandler.ktS R$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\CommandHandler.ktY X$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\ConfigCommandHandler.ktY X$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\ConfigCommandHandler.kt[ Z$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\ExtendedCommandHandler.kt[ Z$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\ExtendedCommandHandler.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\ServiceCommandHandler.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\ServiceCommandHandler.ktY X$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\SystemCommandHandler.ktY X$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\SystemCommandHandler.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\UpgradeCommandHandler.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\UpgradeCommandHandler.ktX W$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\VideoCommandHandler.ktX W$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\VideoCommandHandler.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\receiver\BootReceiver.ktR Q$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\receiver\BootReceiver.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\receiver\RebootReceiver.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\receiver\RebootReceiver.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\service\UpgradeWatcherService.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\service\UpgradeWatcherService.ktX W$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\service\WebRTCSenderService.ktX W$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\service\WebRTCSenderService.ktX W$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\service\WebRTCSenderService.ktX W$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\service\WebRTCSenderService.ktV U$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\signaling\SignalingClient.ktV U$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\signaling\SignalingClient.kt^ ]$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\signaling\SignalingClientListener.ktN M$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\AppListAdapter.ktN M$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\AppListAdapter.ktS R$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\AppSelectorActivity.ktS R$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\AppSelectorActivity.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\CameraConfigActivity.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\CameraConfigActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\SettingsActivity.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\SettingsActivity.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\AutoRebootManager.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\AutoRebootManager.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\Constants.ktV U$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\DeviceInfoCollector.ktV U$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\DeviceInfoCollector.ktU T$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\DeviceInfoReporter.ktU T$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\DeviceInfoReporter.ktS R$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\DeviceLogManager.ktS R$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\DeviceLogManager.ktN M$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\DeviceUtils.kt\ [$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\FirstInstallConfigManager.kt\ [$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\FirstInstallConfigManager.ktO N$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\GameLauncher.ktM L$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\LogManager.ktI H$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\Logger.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\ScreenshotManager.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCManager.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCManager.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCManager.kte d$PROJECT_DIR$\app\build\generated\source\buildConfig\debug\com\example\webrtcsender\BuildConfig.javaU T$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\ZtlInstallHelper.javaZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\audio\CaptureCardAudioManager.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\audio\CaptureCardAudioManager.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktP O$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCClient.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.kte d$PROJECT_DIR$\app\build\generated\source\buildConfig\debug\com\example\webrtcsender\BuildConfig.javae d$PROJECT_DIR$\app\build\generated\source\buildConfig\debug\com\example\webrtcsender\BuildConfig.javaZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\UpgradeCommandHandler.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\UpgradeCommandHandler.ktU T$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\DeviceInfoReporter.ktU T$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\DeviceInfoReporter.kte d$PROJECT_DIR$\app\build\generated\source\buildConfig\debug\com\example\webrtcsender\BuildConfig.javaZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\UpgradeCommandHandler.ktZ Y$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\UpgradeCommandHandler.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.kte d$PROJECT_DIR$\app\build\generated\source\buildConfig\debug\com\example\webrtcsender\BuildConfig.javae d$PROJECT_DIR$\app\build\generated\source\buildConfig\debug\com\example\webrtcsender\BuildConfig.java[ Z$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\ExtendedCommandHandler.kt[ Z$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\ExtendedCommandHandler.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\ScreenshotManager.kte d$PROJECT_DIR$\app\build\generated\source\buildConfig\debug\com\example\webrtcsender\BuildConfig.javaT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\ScreenshotManager.kte d$PROJECT_DIR$\app\build\generated\source\buildConfig\debug\com\example\webrtcsender\BuildConfig.javaT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\ScreenshotManager.kt[ Z$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\ExtendedCommandHandler.kt[ Z$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\ExtendedCommandHandler.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\ScreenshotManager.kte d$PROJECT_DIR$\app\build\generated\source\buildConfig\debug\com\example\webrtcsender\BuildConfig.javaT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\ScreenshotManager.ktV U$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\CommandDispatcher.ktV U$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\CommandDispatcher.kt[ Z$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\ExtendedCommandHandler.kt[ Z$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\command\ExtendedCommandHandler.ktV U$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\signaling\SignalingClient.ktV U$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\signaling\SignalingClient.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\AutoRebootManager.ktT S$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\AutoRebootManager.ktM L$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\LogManager.ktM L$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\utils\LogManager.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCManager.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCManager.ktQ P$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\webrtc\WebRTCManager.kte d$PROJECT_DIR$\app\build\generated\source\buildConfig\debug\com\example\webrtcsender\BuildConfig.javaL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.ktL K$PROJECT_DIR$\app\src\main\java\com\example\webrtcsender\ui\MainActivity.kte d$PROJECT_DIR$\app\build\generated\source\buildConfig\debug\com\example\webrtcsender\BuildConfig.java