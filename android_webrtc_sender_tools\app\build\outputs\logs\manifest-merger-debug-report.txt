-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:113:9-121:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:117:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:115:13-64
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:116:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:114:13-62
manifest
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:2:1-127:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:2:1-127:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:2:1-127:12
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:2:1-127:12
MERGED from [androidx.fragment:fragment-ktx:1.5.5] C:\Users\<USER>\.gradle\caches\transforms-3\443fadfaf6383bc5b5e588243112a289\transformed\fragment-ktx-1.5.5\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\b21ca2d786db2940501a4481f3d91270\transformed\constraintlayout-2.0.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\555938ff04117f2583e2fd7537eed1ba\transformed\appcompat-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.5] C:\Users\<USER>\.gradle\caches\transforms-3\19105bb98452f22f0264cd38867f7cd7\transformed\fragment-1.5.5\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d555ac39fa90d581317166a31cd4216d\transformed\activity-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\efb9871830b28c1d1ea80f3aacc2a33c\transformed\activity-ktx-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\6aa96da89a547ce51d431de017005840\transformed\lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a40e22bdabd0ae400114ec7348c3dbf7\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\0bcbc2ad0d98a1aa7cc3c6a6edfbc156\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\bca58b9e345f34924ab57a0ee119306f\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\211fa873150dd09c63549363775202f8\transformed\core-ktx-1.3.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb7af77cbb1e8e51d2633496871c4b6b\transformed\lifecycle-runtime-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d7d3932aba4e36e22ba6b8863bb2ca4f\transformed\savedstate-ktx-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\fb71150e50ab9b2463b60463efae3c55\transformed\lifecycle-livedata-core-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0cec391826e4e75ed1fea2bfca6e0093\transformed\savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\97956e8e71500a1ccb2d4ad5038496e8\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e35ac0a4360096fa359c2a9279a5f562\transformed\recyclerview-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.infobip:google-webrtc:1.0.43591] C:\Users\<USER>\.gradle\caches\transforms-3\463f8cc47f2e7dca35640c627c00b1d6\transformed\google-webrtc-1.0.43591\AndroidManifest.xml:1:1-93
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\61d0d4a0489a233e9d82bd5fc3edd70a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db21016315b1f4e17a028d92fb01e933\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a0a96e8819dc0cb87debd8574868a33\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d6a2ca29e73553b84c61087ba85ad702\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f63e9ecad91ec56ea9b0288252d55b1\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c81e35a207e81f033f2451dbd8c22462\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\917ba4ed7542e9ab6345b4ed1eed16af\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\27b29875fb9d96f497ca9abdaab496d4\transformed\core-1.8.0\AndroidManifest.xml:17:1-26:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\ccdaacee6818c55922443033e06f1dd6\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a1fcd94a97cf58f0485c61f4209d3c\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\28aeeb4a1329a5ae40d14b84f09cf01e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e863b3936be007020ddf948bb446eb0\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32a1c7228803db7a8da0b4b288ebb2cf\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\e761d4efd2811a9c903e090db7a92fa7\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\66a7c6e7663269680821d7e4da07b46f\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\da0931b2c387c355a314587735ee4f05\transformed\annotation-experimental-1.1.0\AndroidManifest.xml:17:1-24:12
	package
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:6:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:7:5-76
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:7:22-73
uses-permission#android.permission.CHANGE_NETWORK_STATE
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:9:22-76
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:12:5-65
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:12:22-62
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:13:5-60
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:13:19-57
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:14:5-70
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:14:19-67
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:17:5-71
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:17:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:18:5-80
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:18:22-77
uses-permission#android.permission.CAPTURE_AUDIO_OUTPUT
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:21:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:21:22-76
uses-permission#android.permission.CAPTURE_AUDIO_HOTWORD
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:23:5-80
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:23:22-77
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:26:5-77
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:26:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:27:5-94
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:27:22-91
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:30:5-80
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:30:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:31:5-32:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:32:9-35
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:31:22-78
uses-permission#android.permission.QUERY_ALL_PACKAGES
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:35:5-77
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:35:22-74
uses-permission#android.permission.REQUEST_INSTALL_PACKAGES
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:38:5-83
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:38:22-80
uses-permission#android.permission.REBOOT
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:41:5-65
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:41:22-62
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:44:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:44:22-78
uses-permission#android.permission.SCHEDULE_EXACT_ALARM
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:47:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:47:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:50:5-68
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:50:22-65
queries
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:53:5-60:15
intent#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:55:9-58:18
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:56:13-65
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:56:21-62
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:57:13-73
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:57:23-70
application
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:62:5-125:19
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:62:5-125:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\b21ca2d786db2940501a4481f3d91270\transformed\constraintlayout-2.0.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\b21ca2d786db2940501a4481f3d91270\transformed\constraintlayout-2.0.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\27b29875fb9d96f497ca9abdaab496d4\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\27b29875fb9d96f497ca9abdaab496d4\transformed\core-1.8.0\AndroidManifest.xml:24:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\28aeeb4a1329a5ae40d14b84f09cf01e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\28aeeb4a1329a5ae40d14b84f09cf01e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\27b29875fb9d96f497ca9abdaab496d4\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:68:9-35
	android:label
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:66:9-41
	android:roundIcon
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:67:9-54
	android:icon
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:65:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:64:9-35
	android:theme
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:69:9-50
	android:usesCleartextTraffic
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:70:9-44
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:63:9-40
activity#com.example.webrtcsender.ui.MainActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:72:9-80:20
	android:launchMode
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:75:13-43
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:74:13-36
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:73:13-44
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:76:13-79:29
activity#com.example.webrtcsender.ui.SettingsActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:82:9-86:61
	android:parentActivityName
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:86:13-58
	android:label
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:85:13-51
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:84:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:83:13-48
activity#com.example.webrtcsender.ui.AppSelectorActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:88:9-92:65
	android:parentActivityName
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:92:13-62
	android:label
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:91:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:90:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:89:13-51
activity#com.example.webrtcsender.ui.CameraConfigActivity
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:94:9-98:61
	android:parentActivityName
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:98:13-58
	android:label
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:97:13-34
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:96:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:95:13-52
service#com.example.webrtcsender.service.WebRTCSenderService
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:100:9-104:63
	android:enabled
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:102:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:103:13-37
	android:foregroundServiceType
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:104:13-60
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:101:13-56
service#com.example.webrtcsender.service.UpgradeWatcherService
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:107:9-110:40
	android:enabled
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:109:13-35
	android:exported
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:110:13-37
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:108:13-58
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:118:13-120:54
	android:resource
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:120:17-51
	android:name
		ADDED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:119:17-67
uses-sdk
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml
MERGED from [androidx.fragment:fragment-ktx:1.5.5] C:\Users\<USER>\.gradle\caches\transforms-3\443fadfaf6383bc5b5e588243112a289\transformed\fragment-ktx-1.5.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.5.5] C:\Users\<USER>\.gradle\caches\transforms-3\443fadfaf6383bc5b5e588243112a289\transformed\fragment-ktx-1.5.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\b21ca2d786db2940501a4481f3d91270\transformed\constraintlayout-2.0.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.4] C:\Users\<USER>\.gradle\caches\transforms-3\b21ca2d786db2940501a4481f3d91270\transformed\constraintlayout-2.0.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\555938ff04117f2583e2fd7537eed1ba\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\555938ff04117f2583e2fd7537eed1ba\transformed\appcompat-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.5] C:\Users\<USER>\.gradle\caches\transforms-3\19105bb98452f22f0264cd38867f7cd7\transformed\fragment-1.5.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.5] C:\Users\<USER>\.gradle\caches\transforms-3\19105bb98452f22f0264cd38867f7cd7\transformed\fragment-1.5.5\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d555ac39fa90d581317166a31cd4216d\transformed\activity-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\d555ac39fa90d581317166a31cd4216d\transformed\activity-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\efb9871830b28c1d1ea80f3aacc2a33c\transformed\activity-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\efb9871830b28c1d1ea80f3aacc2a33c\transformed\activity-ktx-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\6aa96da89a547ce51d431de017005840\transformed\lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\6aa96da89a547ce51d431de017005840\transformed\lifecycle-viewmodel-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a40e22bdabd0ae400114ec7348c3dbf7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a40e22bdabd0ae400114ec7348c3dbf7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\0bcbc2ad0d98a1aa7cc3c6a6edfbc156\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\0bcbc2ad0d98a1aa7cc3c6a6edfbc156\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\bca58b9e345f34924ab57a0ee119306f\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\bca58b9e345f34924ab57a0ee119306f\transformed\lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\211fa873150dd09c63549363775202f8\transformed\core-ktx-1.3.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.core:core-ktx:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\211fa873150dd09c63549363775202f8\transformed\core-ktx-1.3.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb7af77cbb1e8e51d2633496871c4b6b\transformed\lifecycle-runtime-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\cb7af77cbb1e8e51d2633496871c4b6b\transformed\lifecycle-runtime-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d7d3932aba4e36e22ba6b8863bb2ca4f\transformed\savedstate-ktx-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\d7d3932aba4e36e22ba6b8863bb2ca4f\transformed\savedstate-ktx-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\fb71150e50ab9b2463b60463efae3c55\transformed\lifecycle-livedata-core-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\fb71150e50ab9b2463b60463efae3c55\transformed\lifecycle-livedata-core-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0cec391826e4e75ed1fea2bfca6e0093\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\0cec391826e4e75ed1fea2bfca6e0093\transformed\savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\97956e8e71500a1ccb2d4ad5038496e8\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\97956e8e71500a1ccb2d4ad5038496e8\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e35ac0a4360096fa359c2a9279a5f562\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\e35ac0a4360096fa359c2a9279a5f562\transformed\recyclerview-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\61d0d4a0489a233e9d82bd5fc3edd70a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\61d0d4a0489a233e9d82bd5fc3edd70a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db21016315b1f4e17a028d92fb01e933\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db21016315b1f4e17a028d92fb01e933\transformed\appcompat-resources-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a0a96e8819dc0cb87debd8574868a33\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a0a96e8819dc0cb87debd8574868a33\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d6a2ca29e73553b84c61087ba85ad702\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d6a2ca29e73553b84c61087ba85ad702\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f63e9ecad91ec56ea9b0288252d55b1\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f63e9ecad91ec56ea9b0288252d55b1\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c81e35a207e81f033f2451dbd8c22462\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c81e35a207e81f033f2451dbd8c22462\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\917ba4ed7542e9ab6345b4ed1eed16af\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\917ba4ed7542e9ab6345b4ed1eed16af\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\27b29875fb9d96f497ca9abdaab496d4\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\27b29875fb9d96f497ca9abdaab496d4\transformed\core-1.8.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\ccdaacee6818c55922443033e06f1dd6\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\ccdaacee6818c55922443033e06f1dd6\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a1fcd94a97cf58f0485c61f4209d3c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\53a1fcd94a97cf58f0485c61f4209d3c\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\28aeeb4a1329a5ae40d14b84f09cf01e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\28aeeb4a1329a5ae40d14b84f09cf01e\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e863b3936be007020ddf948bb446eb0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e863b3936be007020ddf948bb446eb0\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32a1c7228803db7a8da0b4b288ebb2cf\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\32a1c7228803db7a8da0b4b288ebb2cf\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\e761d4efd2811a9c903e090db7a92fa7\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\e761d4efd2811a9c903e090db7a92fa7\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\66a7c6e7663269680821d7e4da07b46f\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\66a7c6e7663269680821d7e4da07b46f\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\da0931b2c387c355a314587735ee4f05\transformed\annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.annotation:annotation-experimental:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\da0931b2c387c355a314587735ee4f05\transformed\annotation-experimental-1.1.0\AndroidManifest.xml:20:5-22:41
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml
uses-permission#android.permission.READ_PHONE_STATE
IMPLIED from C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:2:1-127:12 reason: com.infobip has a targetSdkVersion < 4
