1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.webrtcsender"
4    android:versionCode="114"
5    android:versionName="1.1.4" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="33" />
10
11    <!-- 网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:5:5-67
12-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:5:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:6:5-79
13-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:6:22-76
14    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
14-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:7:5-76
14-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:7:22-73
15    <!-- IPv6网络支持 -->
16    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
16-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:9:5-79
16-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:9:22-76
17
18    <!-- 摄像头权限 -->
19    <uses-permission android:name="android.permission.CAMERA" />
19-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:12:5-65
19-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:12:22-62
20
21    <uses-feature android:name="android.hardware.camera" />
21-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:13:5-60
21-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:13:19-57
22    <uses-feature android:name="android.hardware.camera.autofocus" />
22-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:14:5-70
22-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:14:19-67
23
24    <!-- 麦克风权限 -->
25    <uses-permission android:name="android.permission.RECORD_AUDIO" />
25-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:17:5-71
25-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:17:22-68
26    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
26-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:18:5-80
26-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:18:22-77
27
28    <!-- 系统音频录制权限 (Android 10+) -->
29    <uses-permission android:name="android.permission.CAPTURE_AUDIO_OUTPUT" />
29-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:21:5-79
29-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:21:22-76
30    <!-- 音频来源测试权限 -->
31    <uses-permission android:name="android.permission.CAPTURE_AUDIO_HOTWORD" />
31-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:23:5-80
31-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:23:22-77
32
33    <!-- 屏幕录制权限 -->
34    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
34-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:26:5-77
34-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:26:22-74
35    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
35-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:27:5-94
35-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:27:22-91
36
37    <!-- 存储权限 -->
38    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
38-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:30:5-80
38-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:30:22-77
39    <uses-permission
39-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:31:5-32:38
40        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
40-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:31:22-78
41        android:maxSdkVersion="28" />
41-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:32:9-35
42
43    <!-- 查询应用列表权限 (Android 11+) -->
44    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
44-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:35:5-77
44-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:35:22-74
45
46    <!-- 安装APK权限 -->
47    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
47-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:38:5-83
47-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:38:22-80
48
49    <!-- 系统重启权限 -->
50    <uses-permission android:name="android.permission.REBOOT" />
50-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:41:5-65
50-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:41:22-62
51
52    <!-- 开机自启动权限 -->
53    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
53-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:44:5-81
53-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:44:22-78
54
55    <!-- 精确闹钟权限 (Android 12+) -->
56    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
56-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:47:5-79
56-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:47:22-76
57
58    <!-- 唤醒锁权限 -->
59    <uses-permission android:name="android.permission.WAKE_LOCK" />
59-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:50:5-68
59-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:50:22-65
60
61    <!-- 查询特定应用的声明 -->
62    <queries>
62-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:53:5-60:15
63
64        <!-- 查询所有LAUNCHER应用 -->
65        <intent>
65-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:55:9-58:18
66            <action android:name="android.intent.action.MAIN" />
66-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:56:13-65
66-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:56:21-62
67
68            <category android:name="android.intent.category.LAUNCHER" />
68-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:57:13-73
68-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:57:23-70
69        </intent>
70    </queries>
71
72    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
73
74    <application
74-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:62:5-125:19
75        android:name="com.example.webrtcsender.WebRTCSenderApp"
75-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:63:9-40
76        android:allowBackup="true"
76-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:64:9-35
77        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
77-->[androidx.core:core:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\27b29875fb9d96f497ca9abdaab496d4\transformed\core-1.8.0\AndroidManifest.xml:24:18-86
78        android:debuggable="true"
79        android:extractNativeLibs="true"
80        android:icon="@mipmap/ic_launcher"
80-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:65:9-43
81        android:label="@string/app_name"
81-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:66:9-41
82        android:roundIcon="@mipmap/ic_launcher_round"
82-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:67:9-54
83        android:supportsRtl="true"
83-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:68:9-35
84        android:theme="@style/Theme.WebRTCSender"
84-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:69:9-50
85        android:usesCleartextTraffic="true" >
85-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:70:9-44
86        <activity
86-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:72:9-80:20
87            android:name="com.example.webrtcsender.ui.MainActivity"
87-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:73:13-44
88            android:exported="true"
88-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:74:13-36
89            android:launchMode="singleTop" >
89-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:75:13-43
90            <intent-filter>
90-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:76:13-79:29
91                <action android:name="android.intent.action.MAIN" />
91-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:56:13-65
91-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:56:21-62
92
93                <category android:name="android.intent.category.LAUNCHER" />
93-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:57:13-73
93-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:57:23-70
94            </intent-filter>
95        </activity>
96        <activity
96-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:82:9-86:61
97            android:name="com.example.webrtcsender.ui.SettingsActivity"
97-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:83:13-48
98            android:exported="false"
98-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:84:13-37
99            android:label="@string/settings_title"
99-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:85:13-51
100            android:parentActivityName="com.example.webrtcsender.ui.MainActivity" />
100-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:86:13-58
101        <activity
101-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:88:9-92:65
102            android:name="com.example.webrtcsender.ui.AppSelectorActivity"
102-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:89:13-51
103            android:exported="false"
103-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:90:13-37
104            android:label="选择游戏应用"
104-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:91:13-35
105            android:parentActivityName="com.example.webrtcsender.ui.SettingsActivity" />
105-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:92:13-62
106        <activity
106-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:94:9-98:61
107            android:name="com.example.webrtcsender.ui.CameraConfigActivity"
107-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:95:13-52
108            android:exported="false"
108-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:96:13-37
109            android:label="摄像头配置"
109-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:97:13-34
110            android:parentActivityName="com.example.webrtcsender.ui.MainActivity" />
110-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:98:13-58
111
112        <service
112-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:100:9-104:63
113            android:name="com.example.webrtcsender.service.WebRTCSenderService"
113-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:101:13-56
114            android:enabled="true"
114-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:102:13-35
115            android:exported="false"
115-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:103:13-37
116            android:foregroundServiceType="mediaProjection" />
116-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:104:13-60
117
118        <!-- 升级监控服务 -->
119        <service
119-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:107:9-110:40
120            android:name="com.example.webrtcsender.service.UpgradeWatcherService"
120-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:108:13-58
121            android:enabled="true"
121-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:109:13-35
122            android:exported="false" />
122-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:110:13-37
123
124        <!-- FileProvider for APK installation -->
125        <provider
126            android:name="androidx.core.content.FileProvider"
126-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:114:13-62
127            android:authorities="com.example.webrtcsender.fileprovider"
127-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:115:13-64
128            android:exported="false"
128-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:116:13-37
129            android:grantUriPermissions="true" >
129-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:117:13-47
130            <meta-data
130-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:118:13-120:54
131                android:name="android.support.FILE_PROVIDER_PATHS"
131-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:119:17-67
132                android:resource="@xml/file_paths" />
132-->C:\Users\<USER>\Documents\augment-projects\miniupnpc\android_webrtc_sender_tools\app\src\main\AndroidManifest.xml:120:17-51
133        </provider>
134    </application>
135
136</manifest>
