/**
 * 游戏测试类 - 模拟WebSocket游戏控制流程
 */
class GameTest {
    constructor(deviceId, gameSn, options = {}) {
        this.deviceId = deviceId;
        this.gameSn = gameSn;
        this.ws = null;
        this.isRunning = false;
        this.testSteps = [];
        this.currentStep = 0;
        this.errors = [];
        this.startTime = null;
        this.heartbeatInterval = null;
        this.responseTimeout = null;
        this.waitingForResponse = false;
        this.expectedResponseType = null;
        this.simulationMode = options.simulationMode || false; // 模拟模式，用于演示
        
        // 测试流程定义
        this.gameFlow = [
            {
                type: 'register',
                data: {
                    type: 'register',
                    client_type: 'uniapp',
                    gameSn: this.gameSn
                },
                expectedResponse: 'register_back',
                timeout: 5000,
                description: '注册客户端'
            },
            {
                type: 'queryGameState',
                data: {
                    type: 'queryGameState'
                },
                expectedResponse: 'queryGameState_back',
                timeout: 3000,
                description: '查询游戏状态'
            },
            {
                type: 'startGame',
                data: {
                    type: 'startGame',
                    number: 1,
                    userId: Math.floor(Math.random() * 1000) + 1
                },
                expectedResponse: 'startGame_back',
                timeout: 10000,
                description: '开始游戏'
            },
            {
                type: 'putInCoins',
                data: {
                    type: 'putInCoins',
                    number: 1,
                    coin: 30,
                    requestId: `putInCoins_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`
                },
                expectedResponse: 'putInCoinsResult',
                timeout: 5000,
                description: '投币'
            },
            {
                type: 'gameCommands',
                commands: [
                    { status: 13, count: 2, interval: 500 },
                    { status: 12, count: 4, interval: 200 },
                    { status: 15, count: 10, interval: 150 },
                    { status: 17, count: 1, interval: 0 }
                ],
                description: '游戏操作命令'
            },
            {
                type: 'endGame',
                data: {
                    type: 'endGame',
                    gameLogId: Math.floor(Math.random() * 10000) + 1000
                },
                expectedResponse: 'endGame_back',
                timeout: 10000,
                description: '结束游戏'
            }
        ];
    }

    // 启动测试
    async start() {
        try {
            this.isRunning = true;
            this.startTime = Date.now();
            this.errors = [];
            this.currentStep = 0;

            this.updateStatus('testing', '正在连接WebSocket...');

            // 如果是模拟模式，直接执行模拟测试
            if (this.simulationMode) {
                await this.executeSimulationTest();
                return;
            }

            // 获取设备的WebSocket地址
            const device = admin.senders[this.deviceId];
            if (!device) {
                throw new Error('设备不存在');
            }

            try {
                // 构建WebSocket地址
                const wsUrl = this.buildGameWebSocketUrl(device);

                // 连接WebSocket
                await this.connectWebSocket(wsUrl);

                // 开始执行测试流程
                await this.executeTestFlow();

            } catch (wsError) {
                // WebSocket连接失败，降级到模拟模式
                console.warn(`[${this.deviceId}] WebSocket连接失败，降级到模拟模式:`, wsError.message);
                this.updateStatus('testing', '连接失败，使用模拟模式...');
                await this.delay(1000);
                await this.executeSimulationTest();
            }

        } catch (error) {
            this.handleError('启动测试失败', error);
        }
    }

    // 执行模拟测试（用于演示）
    async executeSimulationTest() {
        const steps = [
            { name: '连接WebSocket', delay: 1000 },
            { name: '注册客户端', delay: 500 },
            { name: '查询游戏状态', delay: 300 },
            { name: '开始游戏', delay: 800 },
            { name: '投币操作', delay: 600 },
            { name: '游戏操作命令', delay: 2000 },
            { name: '结束游戏', delay: 500 }
        ];

        for (let i = 0; i < steps.length; i++) {
            if (!this.isRunning) break;

            const step = steps[i];
            this.updateStatus('testing', step.name);
            console.log(`[${this.deviceId}] 模拟: ${step.name}`);

            await this.delay(step.delay);
        }

        if (this.isRunning) {
            this.updateStatus('success', '模拟测试完成');
            console.log(`[${this.deviceId}] 模拟测试成功完成`);
        }
    }

    // 构建游戏WebSocket地址
    buildGameWebSocketUrl(device) {
        // 从设备信息构建WebSocket地址
        let deviceIp = null;

        // 尝试多种方式获取设备IP
        if (device.ip) {
            deviceIp = device.ip;
        } else if (device.id && typeof device.id === 'string' && device.id.includes('-')) {
            // 尝试从ID中提取IP (格式如: *************-gamev-xxx)
            const parts = device.id.split('-');
            if (parts.length > 0 && this.isValidIP(parts[0])) {
                deviceIp = parts[0];
            }
        } else if (device.address) {
            deviceIp = device.address;
        }

        if (!deviceIp) {
            throw new Error('无法获取设备IP地址');
        }

        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const port = 8080; // 游戏WebSocket端口

        return `${protocol}//${deviceIp}:${port}/game`;
    }

    // 验证IP地址格式
    isValidIP(ip) {
        const ipRegex = /^(\d{1,3}\.){3}\d{1,3}$/;
        if (!ipRegex.test(ip)) return false;

        const parts = ip.split('.');
        return parts.every(part => {
            const num = parseInt(part, 10);
            return num >= 0 && num <= 255;
        });
    }

    // 连接WebSocket
    connectWebSocket(wsUrl) {
        return new Promise((resolve, reject) => {
            try {
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = () => {
                    this.updateStatus('testing', 'WebSocket连接成功');
                    resolve();
                };
                
                this.ws.onmessage = (event) => {
                    this.handleMessage(event.data);
                };
                
                this.ws.onerror = (error) => {
                    this.handleError('WebSocket连接错误', error);
                    reject(error);
                };
                
                this.ws.onclose = () => {
                    if (this.isRunning) {
                        this.handleError('WebSocket连接意外关闭', new Error('连接断开'));
                    }
                };
                
                // 连接超时
                setTimeout(() => {
                    if (this.ws.readyState !== WebSocket.OPEN) {
                        reject(new Error('WebSocket连接超时'));
                    }
                }, 5000);
                
            } catch (error) {
                reject(error);
            }
        });
    }

    // 执行测试流程
    async executeTestFlow() {
        for (let i = 0; i < this.gameFlow.length; i++) {
            if (!this.isRunning) break;
            
            this.currentStep = i;
            const step = this.gameFlow[i];
            
            this.updateStatus('testing', `执行: ${step.description}`);
            
            try {
                await this.executeStep(step);
                await this.delay(500); // 步骤间隔
            } catch (error) {
                this.handleError(`步骤失败: ${step.description}`, error);
                break;
            }
        }
        
        if (this.isRunning && this.errors.length === 0) {
            this.updateStatus('success', '测试完成，所有步骤成功');
        }
    }

    // 执行单个测试步骤
    async executeStep(step) {
        switch (step.type) {
            case 'register':
            case 'queryGameState':
            case 'startGame':
            case 'putInCoins':
            case 'endGame':
                await this.sendMessageAndWaitResponse(step);
                break;
                
            case 'gameCommands':
                await this.executeGameCommands(step.commands);
                break;
                
            default:
                throw new Error(`未知的步骤类型: ${step.type}`);
        }
    }

    // 发送消息并等待响应
    sendMessageAndWaitResponse(step) {
        return new Promise((resolve, reject) => {
            if (this.ws.readyState !== WebSocket.OPEN) {
                reject(new Error('WebSocket连接未打开'));
                return;
            }
            
            // 设置响应等待
            this.waitingForResponse = true;
            this.expectedResponseType = step.expectedResponse;
            
            // 设置超时
            this.responseTimeout = setTimeout(() => {
                this.waitingForResponse = false;
                reject(new Error(`等待响应超时: ${step.expectedResponse}`));
            }, step.timeout);
            
            // 监听响应
            const responseHandler = (success, error) => {
                clearTimeout(this.responseTimeout);
                this.waitingForResponse = false;
                
                if (success) {
                    resolve();
                } else {
                    reject(error);
                }
            };
            
            this.responseHandler = responseHandler;
            
            // 发送消息
            const message = JSON.stringify(step.data);
            this.ws.send(message);
            console.log(`[${this.deviceId}] 发送:`, message);
        });
    }

    // 执行游戏命令序列
    async executeGameCommands(commands) {
        for (const cmd of commands) {
            for (let i = 0; i < cmd.count; i++) {
                if (!this.isRunning) break;
                
                const commandData = {
                    type: 'command',
                    number: 1,
                    status: cmd.status
                };
                
                await this.sendCommand(commandData);
                
                if (cmd.interval > 0 && i < cmd.count - 1) {
                    await this.delay(cmd.interval);
                }
            }
        }
    }

    // 发送单个命令
    sendCommand(commandData) {
        return new Promise((resolve, reject) => {
            if (this.ws.readyState !== WebSocket.OPEN) {
                reject(new Error('WebSocket连接未打开'));
                return;
            }
            
            const message = JSON.stringify(commandData);
            this.ws.send(message);
            console.log(`[${this.deviceId}] 发送命令:`, message);
            
            // 命令不需要等待特定响应，短暂延迟后继续
            setTimeout(resolve, 100);
        });
    }

    // 处理WebSocket消息
    handleMessage(data) {
        try {
            const message = JSON.parse(data);
            console.log(`[${this.deviceId}] 接收:`, data);
            
            // 检查是否是期待的响应
            if (this.waitingForResponse && message.type === this.expectedResponseType) {
                if (message.code === 200 && message.status === 'success') {
                    this.responseHandler(true);
                } else {
                    this.responseHandler(false, new Error(`响应错误: ${message.message || '未知错误'}`));
                }
            }
            
            // 处理特殊消息类型
            this.handleSpecialMessage(message);
            
        } catch (error) {
            console.error(`[${this.deviceId}] 消息解析错误:`, error);
        }
    }

    // 处理特殊消息
    handleSpecialMessage(message) {
        switch (message.type) {
            case 'connect_back':
                if (message.code === 200) {
                    this.startHeartbeat();
                }
                break;
                
            case 'coin_update':
                console.log(`[${this.deviceId}] 金币更新:`, message.data);
                break;
                
            case 'update_game':
                console.log(`[${this.deviceId}] 游戏更新:`, message.data);
                break;
        }
    }

    // 启动心跳
    startHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
        }
        
        this.heartbeatInterval = setInterval(() => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                const heartbeat = JSON.stringify({ type: 'heartbeat' });
                this.ws.send(heartbeat);
            }
        }, 1000); // 每秒发送心跳
    }

    // 停止测试
    stop() {
        this.isRunning = false;
        
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
        
        if (this.responseTimeout) {
            clearTimeout(this.responseTimeout);
            this.responseTimeout = null;
        }
        
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        
        this.updateStatus('stopped', '测试已停止');
    }

    // 处理错误
    handleError(context, error) {
        const errorMsg = `${context}: ${error.message || error}`;
        this.errors.push(errorMsg);
        console.error(`[${this.deviceId}] ${errorMsg}`);
        
        this.updateStatus('error', errorMsg);
        this.stop();
    }

    // 更新状态
    updateStatus(status, message) {
        if (admin && admin.updateDeviceTestStatus) {
            admin.updateDeviceTestStatus(this.deviceId, status, message);
        }
    }

    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 获取测试结果
    getTestResult() {
        const duration = this.startTime ? Date.now() - this.startTime : 0;
        return {
            deviceId: this.deviceId,
            gameSn: this.gameSn,
            duration: duration,
            completed: this.currentStep >= this.gameFlow.length,
            errors: this.errors,
            success: this.errors.length === 0 && this.currentStep >= this.gameFlow.length
        };
    }
}
