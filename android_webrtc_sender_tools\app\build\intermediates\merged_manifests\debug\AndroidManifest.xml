<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.webrtcsender"
    android:versionCode="113"
    android:versionName="1.1.3" >

    <uses-sdk
        android:minSdkVersion="21"
        android:targetSdkVersion="33" />

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <!-- IPv6网络支持 -->
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />

    <!-- 摄像头权限 -->
    <uses-permission android:name="android.permission.CAMERA" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <!-- 麦克风权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />

    <!-- 系统音频录制权限 (Android 10+) -->
    <uses-permission android:name="android.permission.CAPTURE_AUDIO_OUTPUT" />
    <!-- 音频来源测试权限 -->
    <uses-permission android:name="android.permission.CAPTURE_AUDIO_HOTWORD" />

    <!-- 屏幕录制权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />

    <!-- 存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="28" />

    <!-- 查询应用列表权限 (Android 11+) -->
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />

    <!-- 安装APK权限 -->
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />

    <!-- 系统重启权限 -->
    <uses-permission android:name="android.permission.REBOOT" />

    <!-- 开机自启动权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <!-- 精确闹钟权限 (Android 12+) -->
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

    <!-- 唤醒锁权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <!-- 查询特定应用的声明 -->
    <queries>

        <!-- 查询所有LAUNCHER应用 -->
        <intent>
            <action android:name="android.intent.action.MAIN" />

            <category android:name="android.intent.category.LAUNCHER" />
        </intent>
    </queries>

    <uses-permission android:name="android.permission.READ_PHONE_STATE" />

    <application
        android:name="com.example.webrtcsender.WebRTCSenderApp"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.WebRTCSender"
        android:usesCleartextTraffic="true" >
        <activity
            android:name="com.example.webrtcsender.ui.MainActivity"
            android:exported="true"
            android:launchMode="singleTop" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.example.webrtcsender.ui.SettingsActivity"
            android:exported="false"
            android:label="@string/settings_title"
            android:parentActivityName="com.example.webrtcsender.ui.MainActivity" />
        <activity
            android:name="com.example.webrtcsender.ui.AppSelectorActivity"
            android:exported="false"
            android:label="选择游戏应用"
            android:parentActivityName="com.example.webrtcsender.ui.SettingsActivity" />
        <activity
            android:name="com.example.webrtcsender.ui.CameraConfigActivity"
            android:exported="false"
            android:label="摄像头配置"
            android:parentActivityName="com.example.webrtcsender.ui.MainActivity" />

        <service
            android:name="com.example.webrtcsender.service.WebRTCSenderService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaProjection" />

        <!-- 升级监控服务 -->
        <service
            android:name="com.example.webrtcsender.service.UpgradeWatcherService"
            android:enabled="true"
            android:exported="false" />

        <!-- FileProvider for APK installation -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.example.webrtcsender.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>
    </application>

</manifest>