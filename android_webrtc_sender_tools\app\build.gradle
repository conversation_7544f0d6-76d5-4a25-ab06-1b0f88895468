plugins {
    id 'com.android.application'
    id 'kotlin-android'
}

android {
    compileSdkVersion 33
    buildToolsVersion "33.0.1"
    namespace "com.example.webrtcsender"

    defaultConfig {
        applicationId "com.example.webrtcsender"
        minSdkVersion 21
        targetSdkVersion 33
        versionCode 114
        versionName "1.1.4"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    // 移除视图绑定配置
    buildFeatures {
        viewBinding false
        buildConfig true  // 启用BuildConfig
    }
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib:1.5.31"
    implementation 'androidx.core:core-ktx:1.3.2'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'androidx.activity:activity-ktx:1.6.1'
    implementation 'androidx.fragment:fragment-ktx:1.5.5'

    // WebRTC库
    implementation "com.infobip:google-webrtc:1.0.43591"

    // 🎵 音频编码库 - 使用Android内置MediaRecorder录制MP3

    // 协程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.0'

    // WebSocket
    implementation 'org.java-websocket:Java-WebSocket:1.5.3'

    // JSON处理
    implementation 'com.google.code.gson:gson:2.8.8'

    // HTTP客户端
    implementation 'com.squareup.okhttp3:okhttp:3.14.9'

    // FTP客户端
    implementation 'commons-net:commons-net:3.6'

    testImplementation 'junit:junit:4.13.2'
    // Android 测试相关依赖
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'

    
    implementation files("libs\\ZtlApi.jar")
}
