<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏测试功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f1f3f4;
        }

        .header h1 {
            color: #495057;
            margin-bottom: 10px;
        }

        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .demo-section h2 {
            color: #495057;
            margin-bottom: 15px;
            border-bottom: 2px solid #667eea;
            padding-bottom: 8px;
        }

        .device-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .device-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .device-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.12);
        }

        .device-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .device-id {
            font-weight: 600;
            color: #495057;
        }

        .device-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-online {
            background: #d4edda;
            color: #155724;
        }

        .status-offline {
            background: #f8d7da;
            color: #721c24;
        }

        .device-info {
            margin-bottom: 15px;
            font-size: 14px;
            color: #6c757d;
        }

        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .test-status {
            font-size: 12px;
            margin-top: 4px;
            padding: 4px 8px;
            border-radius: 3px;
            display: inline-block;
        }

        .test-status.testing {
            color: #2196F3;
            background-color: rgba(33, 150, 243, 0.1);
            animation: pulse 2s infinite;
        }

        .test-status.success {
            color: #4CAF50;
            background-color: rgba(76, 175, 80, 0.1);
        }

        .test-status.error {
            color: #F44336;
            background-color: rgba(244, 67, 54, 0.1);
        }

        .test-status.stopped {
            color: #9E9E9E;
            background-color: rgba(158, 158, 158, 0.1);
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .batch-controls {
            text-align: center;
            margin-bottom: 20px;
        }

        .batch-btn {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            margin: 0 10px;
            transition: all 0.2s;
        }

        .batch-btn:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-success { color: #68d391; }
        .log-error { color: #fc8181; }
        .log-info { color: #63b3ed; }
        .log-warning { color: #f6e05e; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 游戏测试功能演示</h1>
            <p>模拟WebSocket游戏控制流程的批量测试功能</p>
        </div>

        <div class="demo-section">
            <h2>批量控制</h2>
            <div class="batch-controls">
                <button class="batch-btn" onclick="startBatchTest()">🚀 启动批量测试</button>
                <button class="batch-btn" onclick="stopAllTests()" style="background: #dc3545;">🛑 停止所有测试</button>
            </div>
        </div>

        <div class="demo-section">
            <h2>设备列表</h2>
            <div class="device-grid" id="deviceGrid">
                <!-- 设备卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <div class="demo-section">
            <h2>测试日志</h2>
            <div class="log-area" id="logArea">
                <div class="log-entry log-info">[系统] 游戏测试功能演示已启动</div>
            </div>
        </div>
    </div>

    <script>
        // 模拟设备数据
        const mockDevices = [
            { id: '*************-game1', ip: '*************', gameSn: '3bbe432da0d29d4e', online: true, name: '游戏机1号' },
            { id: '*************-game2', ip: '*************', gameSn: '4ccf543eb1e3a5f9', online: true, name: '游戏机2号' },
            { id: '*************-game3', ip: '*************', gameSn: '5ddf654fc2f4b6ga', online: false, name: '游戏机3号' },
            { id: '*************-game4', ip: '*************', gameSn: '6eef765gd3g5c7hb', online: true, name: '游戏机4号' }
        ];

        // 游戏测试实例存储
        const gameTests = new Map();

        // 初始化页面
        function initDemo() {
            renderDevices();
            log('系统', '演示页面初始化完成', 'info');
        }

        // 渲染设备卡片
        function renderDevices() {
            const grid = document.getElementById('deviceGrid');
            grid.innerHTML = '';

            mockDevices.forEach(device => {
                const card = createDeviceCard(device);
                grid.appendChild(card);
            });
        }

        // 创建设备卡片
        function createDeviceCard(device) {
            const card = document.createElement('div');
            card.className = 'device-card';
            card.setAttribute('data-device-id', device.id);

            card.innerHTML = `
                <div class="device-header">
                    <div class="device-id">${device.name}</div>
                    <div class="device-status ${device.online ? 'status-online' : 'status-offline'}">
                        ${device.online ? '在线' : '离线'}
                    </div>
                </div>
                <div class="device-info">
                    <div>设备ID: ${device.id}</div>
                    <div>IP地址: ${device.ip}</div>
                    <div>游戏序列号: ${device.gameSn}</div>
                </div>
                <div class="test-controls">
                    <button class="btn btn-primary" onclick="startSingleTest('${device.id}')" ${!device.online ? 'disabled' : ''}>
                        🎯 开始测试
                    </button>
                    <button class="btn btn-danger" onclick="stopSingleTest('${device.id}')">
                        🛑 停止测试
                    </button>
                </div>
                <div class="test-status-container"></div>
            `;

            return card;
        }

        // 启动单个设备测试
        function startSingleTest(deviceId) {
            const device = mockDevices.find(d => d.id === deviceId);
            if (!device || !device.online) {
                log('测试', `设备 ${deviceId} 离线或不存在`, 'error');
                return;
            }

            log('测试', `开始测试设备 ${deviceId}`, 'info');
            updateTestStatus(deviceId, 'testing', '正在连接...');

            // 模拟测试过程
            simulateGameTest(deviceId, device);
        }

        // 停止单个设备测试
        function stopSingleTest(deviceId) {
            const test = gameTests.get(deviceId);
            if (test) {
                clearTimeout(test.timeout);
                gameTests.delete(deviceId);
                updateTestStatus(deviceId, 'stopped', '测试已停止');
                log('测试', `设备 ${deviceId} 测试已停止`, 'warning');
            }
        }

        // 启动批量测试
        function startBatchTest() {
            const onlineDevices = mockDevices.filter(d => d.online);
            log('批量测试', `开始批量测试，共 ${onlineDevices.length} 个在线设备`, 'info');

            onlineDevices.forEach(device => {
                startSingleTest(device.id);
            });
        }

        // 停止所有测试
        function stopAllTests() {
            gameTests.forEach((test, deviceId) => {
                stopSingleTest(deviceId);
            });
            log('批量测试', '所有测试已停止', 'warning');
        }

        // 模拟游戏测试流程
        function simulateGameTest(deviceId, device) {
            const steps = [
                { name: '连接WebSocket', delay: 1000 },
                { name: '注册客户端', delay: 500 },
                { name: '查询游戏状态', delay: 300 },
                { name: '开始游戏', delay: 800 },
                { name: '投币操作', delay: 600 },
                { name: '游戏操作', delay: 2000 },
                { name: '结束游戏', delay: 500 }
            ];

            let currentStep = 0;
            let totalDelay = 0;

            const executeStep = () => {
                if (currentStep >= steps.length) {
                    updateTestStatus(deviceId, 'success', '测试完成');
                    log('测试', `设备 ${deviceId} 测试成功完成`, 'success');
                    gameTests.delete(deviceId);
                    return;
                }

                const step = steps[currentStep];
                updateTestStatus(deviceId, 'testing', step.name);
                log('测试', `设备 ${deviceId}: ${step.name}`, 'info');

                totalDelay += step.delay;
                currentStep++;

                const timeout = setTimeout(executeStep, step.delay);
                gameTests.set(deviceId, { timeout, step: currentStep });
            };

            executeStep();
        }

        // 更新测试状态
        function updateTestStatus(deviceId, status, message) {
            const card = document.querySelector(`[data-device-id="${deviceId}"]`);
            if (!card) return;

            let statusContainer = card.querySelector('.test-status-container');
            if (!statusContainer) {
                statusContainer = document.createElement('div');
                statusContainer.className = 'test-status-container';
                card.appendChild(statusContainer);
            }

            statusContainer.innerHTML = `<div class="test-status ${status}">🎯 ${message}</div>`;
        }

        // 日志记录
        function log(category, message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] [${category}] ${message}`;
            
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initDemo);
    </script>
</body>
</html>
